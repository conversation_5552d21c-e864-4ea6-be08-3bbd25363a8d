package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceQuarterlyAssessment;
import com.hl.archive.domain.entity.PoliceQuarterlyAssessmentToVWjRyjdkhMapper;
import io.github.linpeilie.AutoMapperConfig__557;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__557.class,
    uses = {PoliceQuarterlyAssessmentToVWjRyjdkhMapper.class},
    imports = {}
)
public interface VWjRyjdkhToPoliceQuarterlyAssessmentMapper extends BaseMapper<VWjRyjdkh, PoliceQuarterlyAssessment> {
  @Mapping(
      target = "assessmentResult",
      source = "jgmc"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "assessmentUnit",
      source = "dwmc"
  )
  @Mapping(
      target = "assessmentQuarter",
      source = "jd"
  )
  @Mapping(
      target = "assessmentYear",
      source = "nd"
  )
  PoliceQuarterlyAssessment convert(VWjRyjdkh source);

  @Mapping(
      target = "assessmentResult",
      source = "jgmc"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "assessmentUnit",
      source = "dwmc"
  )
  @Mapping(
      target = "assessmentQuarter",
      source = "jd"
  )
  @Mapping(
      target = "assessmentYear",
      source = "nd"
  )
  PoliceQuarterlyAssessment convert(VWjRyjdkh source,
      @MappingTarget PoliceQuarterlyAssessment target);
}
