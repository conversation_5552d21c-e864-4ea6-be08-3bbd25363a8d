package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceViolationSummary;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-18T13:47:24+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjWgwjdjbToPoliceViolationSummaryMapperImpl implements VWjWgwjdjbToPoliceViolationSummaryMapper {

    @Override
    public PoliceViolationSummary convert(VWjWgwjdjb source) {
        if ( source == null ) {
            return null;
        }

        PoliceViolationSummary policeViolationSummary = new PoliceViolationSummary();

        policeViolationSummary.setAcceptDate( ConversionUtils.strToLocalDate( source.getSlsj() ) );
        policeViolationSummary.setDetentionOrg( source.getLzscdw() );
        policeViolationSummary.setCaseDate( ConversionUtils.strToLocalDate( source.getLasj() ) );
        policeViolationSummary.setInvestigationStart( ConversionUtils.strToLocalDate( source.getDcsj() ) );
        policeViolationSummary.setCaseOrg( source.getLadw() );
        policeViolationSummary.setDetentionDate( ConversionUtils.strToLocalDate( source.getLzscsj() ) );
        policeViolationSummary.setInvestigationResult( source.getDcqk() );
        policeViolationSummary.setTransferDate( ConversionUtils.strToLocalDate( source.getYjsj() ) );
        policeViolationSummary.setPreliminaryOrg( source.getChdw() );
        policeViolationSummary.setClueSource( source.getWtxslymc() );
        policeViolationSummary.setViolationType( source.getWjwflxmc() );
        policeViolationSummary.setViolationFact( source.getWjwfss() );
        policeViolationSummary.setMeetingDate( ConversionUtils.strToLocalDate( source.getHssj() ) );
        policeViolationSummary.setPreliminaryStart( ConversionUtils.strToLocalDate( source.getChsj() ) );
        policeViolationSummary.setReportOrg( source.getDjdwmc() );
        policeViolationSummary.setFoundDate( ConversionUtils.strToLocalDate( source.getFxsj() ) );
        policeViolationSummary.setCaseNo( source.getAjbh() );
        policeViolationSummary.setFoundOrg( source.getFxdw() );
        policeViolationSummary.setPreliminaryResult( source.getChqk() );
        policeViolationSummary.setXxzjbh( source.getXxzjbh() );
        policeViolationSummary.setMeetingOrg( source.getHsdw() );
        policeViolationSummary.setMeetingResult( source.getHsjg() );
        policeViolationSummary.setAcceptOrg( source.getSldw() );
        policeViolationSummary.setTransferOrg( source.getYjdw() );
        policeViolationSummary.setClueContent( source.getWtxsnr() );
        policeViolationSummary.setInvestigationOrg( source.getDcdw() );

        return policeViolationSummary;
    }

    @Override
    public PoliceViolationSummary convert(VWjWgwjdjb source, PoliceViolationSummary target) {
        if ( source == null ) {
            return target;
        }

        target.setAcceptDate( ConversionUtils.strToLocalDate( source.getSlsj() ) );
        target.setDetentionOrg( source.getLzscdw() );
        target.setCaseDate( ConversionUtils.strToLocalDate( source.getLasj() ) );
        target.setInvestigationStart( ConversionUtils.strToLocalDate( source.getDcsj() ) );
        target.setCaseOrg( source.getLadw() );
        target.setDetentionDate( ConversionUtils.strToLocalDate( source.getLzscsj() ) );
        target.setInvestigationResult( source.getDcqk() );
        target.setTransferDate( ConversionUtils.strToLocalDate( source.getYjsj() ) );
        target.setPreliminaryOrg( source.getChdw() );
        target.setClueSource( source.getWtxslymc() );
        target.setViolationType( source.getWjwflxmc() );
        target.setViolationFact( source.getWjwfss() );
        target.setMeetingDate( ConversionUtils.strToLocalDate( source.getHssj() ) );
        target.setPreliminaryStart( ConversionUtils.strToLocalDate( source.getChsj() ) );
        target.setReportOrg( source.getDjdwmc() );
        target.setFoundDate( ConversionUtils.strToLocalDate( source.getFxsj() ) );
        target.setCaseNo( source.getAjbh() );
        target.setFoundOrg( source.getFxdw() );
        target.setPreliminaryResult( source.getChqk() );
        target.setXxzjbh( source.getXxzjbh() );
        target.setMeetingOrg( source.getHsdw() );
        target.setMeetingResult( source.getHsjg() );
        target.setAcceptOrg( source.getSldw() );
        target.setTransferOrg( source.getYjdw() );
        target.setClueContent( source.getWtxsnr() );
        target.setInvestigationOrg( source.getDcdw() );

        return target;
    }
}
