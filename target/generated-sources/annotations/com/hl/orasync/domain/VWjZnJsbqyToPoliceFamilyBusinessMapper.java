package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceFamilyBusiness;
import com.hl.archive.domain.entity.PoliceFamilyBusinessToVWjZnJsbqyMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__555;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__555.class,
    uses = {ConversionUtils.class,PoliceFamilyBusinessToVWjZnJsbqyMapper.class},
    imports = {}
)
public interface VWjZnJsbqyToPoliceFamilyBusinessMapper extends BaseMapper<VWjZnJsbqy, PoliceFamilyBusiness> {
  @Mapping(
      target = "seniorPositionDate",
      source = "gjzwsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "personalContribution",
      source = "grcze",
      qualifiedByName = {"strToBigDecimal"}
  )
  @Mapping(
      target = "establishmentDate",
      source = "clsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "seniorPositionName",
      source = "gjzwmc"
  )
  @Mapping(
      target = "businessScope",
      source = "jyfw"
  )
  @Mapping(
      target = "registrationAddress",
      source = "zcd"
  )
  @Mapping(
      target = "socialCreditCode",
      source = "zch"
  )
  @Mapping(
      target = "investmentDate",
      source = "tzsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "enterpriseType",
      source = "qylxmc"
  )
  @Mapping(
      target = "registeredCapital",
      source = "zczb",
      qualifiedByName = {"strToBigDecimal"}
  )
  @Mapping(
      target = "isShareholder",
      source = "sfgd"
  )
  @Mapping(
      target = "enterpriseStatus",
      source = "qyztmc"
  )
  @Mapping(
      target = "name",
      source = "xmFr"
  )
  @Mapping(
      target = "isSeniorPosition",
      source = "sfdrgjzw"
  )
  @Mapping(
      target = "businessAddress",
      source = "jyd"
  )
  @Mapping(
      target = "enterpriseName",
      source = "qymc"
  )
  @Mapping(
      target = "personalContributionRatio",
      source = "grczbl",
      qualifiedByName = {"strToBigDecimal"}
  )
  PoliceFamilyBusiness convert(VWjZnJsbqy source);

  @Mapping(
      target = "seniorPositionDate",
      source = "gjzwsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "personalContribution",
      source = "grcze",
      qualifiedByName = {"strToBigDecimal"}
  )
  @Mapping(
      target = "establishmentDate",
      source = "clsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "seniorPositionName",
      source = "gjzwmc"
  )
  @Mapping(
      target = "businessScope",
      source = "jyfw"
  )
  @Mapping(
      target = "registrationAddress",
      source = "zcd"
  )
  @Mapping(
      target = "socialCreditCode",
      source = "zch"
  )
  @Mapping(
      target = "investmentDate",
      source = "tzsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "enterpriseType",
      source = "qylxmc"
  )
  @Mapping(
      target = "registeredCapital",
      source = "zczb",
      qualifiedByName = {"strToBigDecimal"}
  )
  @Mapping(
      target = "isShareholder",
      source = "sfgd"
  )
  @Mapping(
      target = "enterpriseStatus",
      source = "qyztmc"
  )
  @Mapping(
      target = "name",
      source = "xmFr"
  )
  @Mapping(
      target = "isSeniorPosition",
      source = "sfdrgjzw"
  )
  @Mapping(
      target = "businessAddress",
      source = "jyd"
  )
  @Mapping(
      target = "enterpriseName",
      source = "qymc"
  )
  @Mapping(
      target = "personalContributionRatio",
      source = "grczbl",
      qualifiedByName = {"strToBigDecimal"}
  )
  PoliceFamilyBusiness convert(VWjZnJsbqy source, @MappingTarget PoliceFamilyBusiness target);
}
