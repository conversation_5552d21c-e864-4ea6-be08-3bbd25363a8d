package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceHealthStatus;
import com.hl.archive.domain.entity.PoliceHealthStatusToVWjBrJkzkMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__557;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__557.class,
    uses = {ConversionUtils.class,PoliceHealthStatusToVWjBrJkzkMapper.class},
    imports = {}
)
public interface VWjBrJkzkToPoliceHealthStatusMapper extends BaseMapper<VWjBrJkzk, PoliceHealthStatus> {
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "diagnosisDate",
      source = "zysj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "illnessName",
      source = "jbmc"
  )
  PoliceHealthStatus convert(VWjBrJkzk source);

  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "diagnosisDate",
      source = "zysj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "illnessName",
      source = "jbmc"
  )
  PoliceHealthStatus convert(VWjBrJkzk source, @MappingTarget PoliceHealthStatus target);
}
