package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceProjectStory;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-18T13:47:25+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjXhjhRxgrXjsjToPoliceProjectStoryMapperImpl implements VWjXhjhRxgrXjsjToPoliceProjectStoryMapper {

    @Override
    public PoliceProjectStory convert(VWjXhjhRxgrXjsj source) {
        if ( source == null ) {
            return null;
        }

        PoliceProjectStory policeProjectStory = new PoliceProjectStory();

        policeProjectStory.setRegisteredBy( source.getDjrxm() );
        policeProjectStory.setRegisterTime( ConversionUtils.strToDate( source.getDjsj() ) );
        policeProjectStory.setTitle( source.getXjsjBt() );
        policeProjectStory.setContent( source.getXjsjNr() );
        policeProjectStory.setXhjhZjbh( source.getJlXxzjbh() );
        policeProjectStory.setZjbh( source.getXxzjbh() );

        return policeProjectStory;
    }

    @Override
    public PoliceProjectStory convert(VWjXhjhRxgrXjsj source, PoliceProjectStory target) {
        if ( source == null ) {
            return target;
        }

        target.setRegisteredBy( source.getDjrxm() );
        target.setRegisterTime( ConversionUtils.strToDate( source.getDjsj() ) );
        target.setTitle( source.getXjsjBt() );
        target.setContent( source.getXjsjNr() );
        target.setXhjhZjbh( source.getJlXxzjbh() );
        target.setZjbh( source.getXxzjbh() );

        return target;
    }
}
