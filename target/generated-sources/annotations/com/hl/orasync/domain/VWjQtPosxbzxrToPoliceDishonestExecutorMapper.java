package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceDishonestExecutor;
import com.hl.archive.domain.entity.PoliceDishonestExecutorToVWjQtPosxbzxrMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__555;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__555.class,
    uses = {ConversionUtils.class,PoliceDishonestExecutorToVWjQtPosxbzxrMapper.class},
    imports = {}
)
public interface VWjQtPosxbzxrToPoliceDishonestExecutorMapper extends BaseMapper<VWjQtPosxbzxr, PoliceDishonestExecutor> {
  @Mapping(
      target = "dishonestReason",
      source = "jtqk"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "executionUnit",
      source = "zxjg"
  )
  @Mapping(
      target = "relationship",
      source = "rygxmc"
  )
  PoliceDishonestExecutor convert(VWjQtPosxbzxr source);

  @Mapping(
      target = "dishonestReason",
      source = "jtqk"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "executionUnit",
      source = "zxjg"
  )
  @Mapping(
      target = "relationship",
      source = "rygxmc"
  )
  PoliceDishonestExecutor convert(VWjQtPosxbzxr source,
      @MappingTarget PoliceDishonestExecutor target);
}
