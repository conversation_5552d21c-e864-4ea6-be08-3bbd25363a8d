package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceOverseasTravel;
import com.hl.archive.domain.entity.PoliceOverseasTravelToVWjBrYscgMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__555;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__555.class,
    uses = {ConversionUtils.class,PoliceOverseasTravelToVWjBrYscgMapper.class},
    imports = {}
)
public interface VWjBrYscgToPoliceOverseasTravelMapper extends BaseMapper<VWjBrYscg, PoliceOverseasTravel> {
  @Mapping(
      target = "passportNumber",
      source = "hzhm"
  )
  @Mapping(
      target = "endDate",
      source = "jssj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "travelReason",
      source = "sy"
  )
  @Mapping(
      target = "approvalAuthority",
      source = "spjgmc"
  )
  @Mapping(
      target = "destinationCountry",
      source = "sdgj"
  )
  @Mapping(
      target = "startDate",
      source = "kssj",
      qualifiedByName = {"strToDate"}
  )
  PoliceOverseasTravel convert(VWjBrYscg source);

  @Mapping(
      target = "passportNumber",
      source = "hzhm"
  )
  @Mapping(
      target = "endDate",
      source = "jssj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "travelReason",
      source = "sy"
  )
  @Mapping(
      target = "approvalAuthority",
      source = "spjgmc"
  )
  @Mapping(
      target = "destinationCountry",
      source = "sdgj"
  )
  @Mapping(
      target = "startDate",
      source = "kssj",
      qualifiedByName = {"strToDate"}
  )
  PoliceOverseasTravel convert(VWjBrYscg source, @MappingTarget PoliceOverseasTravel target);
}
