package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceHkMacauTaiwanPermit;
import com.hl.archive.domain.entity.PoliceHkMacauTaiwanPermitToVWjBrGattxzMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__555;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__555.class,
    uses = {ConversionUtils.class,PoliceHkMacauTaiwanPermitToVWjBrGattxzMapper.class},
    imports = {}
)
public interface VWjBrGattxzToPoliceHkMacauTaiwanPermitMapper extends BaseMapper<VWjBrGattxz, PoliceHkMacauTaiwanPermit> {
  @Mapping(
      target = "expiryDate",
      source = "yxqz",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "documentNumber",
      source = "zjhm"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "documentName",
      source = "zjmc"
  )
  @Mapping(
      target = "issueDate",
      source = "qfrq",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "remarks",
      source = "bz"
  )
  @Mapping(
      target = "custodyOrganization",
      source = "bgjgmc"
  )
  PoliceHkMacauTaiwanPermit convert(VWjBrGattxz source);

  @Mapping(
      target = "expiryDate",
      source = "yxqz",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "documentNumber",
      source = "zjhm"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "documentName",
      source = "zjmc"
  )
  @Mapping(
      target = "issueDate",
      source = "qfrq",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "remarks",
      source = "bz"
  )
  @Mapping(
      target = "custodyOrganization",
      source = "bgjgmc"
  )
  PoliceHkMacauTaiwanPermit convert(VWjBrGattxz source,
      @MappingTarget PoliceHkMacauTaiwanPermit target);
}
