package com.hl.orasync.domain;

import com.hl.archive.domain.entity.AuxiliaryPoliceInfo;
import com.hl.archive.domain.entity.AuxiliaryPoliceInfoToVWjRyjbxxFjxxMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__555;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__555.class,
    uses = {ConversionUtils.class,AuxiliaryPoliceInfoToVWjRyjbxxFjxxMapper.class},
    imports = {}
)
public interface VWjRyjbxxFjxxToAuxiliaryPoliceInfoMapper extends BaseMapper<VWjRyjbxxFjxx, AuxiliaryPoliceInfo> {
  @Mapping(
      target = "specialty",
      source = "zytc"
  )
  @Mapping(
      target = "ethnicity",
      source = "mz"
  )
  @Mapping(
      target = "gender",
      source = "xb"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "hierarchyLevel",
      source = "cjmc"
  )
  @Mapping(
      target = "militaryService",
      source = "sfby"
  )
  @Mapping(
      target = "bloodType",
      source = "xxmc"
  )
  @Mapping(
      target = "employmentStatus",
      source = "rzztmc"
  )
  @Mapping(
      target = "employeeNumber",
      source = "gh"
  )
  @Mapping(
      target = "registeredAddress",
      source = "hjdz"
  )
  @Mapping(
      target = "educationLevel",
      source = "xl"
  )
  @Mapping(
      target = "driverLicense",
      source = "jz"
  )
  @Mapping(
      target = "professionalTitle",
      source = "zc"
  )
  @Mapping(
      target = "startAuxiliaryDate",
      source = "fjgzrq",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "medicalHistory",
      source = "zdbs"
  )
  @Mapping(
      target = "formerName",
      source = "cym"
  )
  @Mapping(
      target = "firstWorkDate",
      source = "scgzsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "partyJoinDate",
      source = "rdsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "laborCompany",
      source = "lwgs"
  )
  @Mapping(
      target = "officePhoneOuter",
      source = "zjWx"
  )
  @Mapping(
      target = "firstAuxiliaryDate",
      source = "scsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "politicalStatus",
      source = "zzmm"
  )
  @Mapping(
      target = "birthDate",
      source = "csrq",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "officePhoneInner",
      source = "zjNx"
  )
  @Mapping(
      target = "phoneNumber",
      source = "sjhm"
  )
  @Mapping(
      target = "healthStatus",
      source = "jkzkmc"
  )
  @Mapping(
      target = "leaderName",
      source = "zrldxm"
  )
  @Mapping(
      target = "leaderPoliceNumber",
      source = "zrldjh"
  )
  @Mapping(
      target = "organization",
      source = "dwmc"
  )
  @Mapping(
      target = "name",
      source = "xm"
  )
  @Mapping(
      target = "nativePlace",
      source = "jgmc"
  )
  @Mapping(
      target = "securityChannel",
      source = "bzqdmc"
  )
  @Mapping(
      target = "position",
      source = "gwmc"
  )
  @Mapping(
      target = "auxiliarySeniority",
      source = "fzgl",
      qualifiedByName = {"bigDecimalToString"}
  )
  @Mapping(
      target = "residenceAddress",
      source = "jzd"
  )
  @Mapping(
      target = "maritalStatus",
      source = "hyzk"
  )
  @Mapping(
      target = "age",
      source = "nl",
      qualifiedByName = {"bigDecimalToString"}
  )
  AuxiliaryPoliceInfo convert(VWjRyjbxxFjxx source);

  @Mapping(
      target = "specialty",
      source = "zytc"
  )
  @Mapping(
      target = "ethnicity",
      source = "mz"
  )
  @Mapping(
      target = "gender",
      source = "xb"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "hierarchyLevel",
      source = "cjmc"
  )
  @Mapping(
      target = "militaryService",
      source = "sfby"
  )
  @Mapping(
      target = "bloodType",
      source = "xxmc"
  )
  @Mapping(
      target = "employmentStatus",
      source = "rzztmc"
  )
  @Mapping(
      target = "employeeNumber",
      source = "gh"
  )
  @Mapping(
      target = "registeredAddress",
      source = "hjdz"
  )
  @Mapping(
      target = "educationLevel",
      source = "xl"
  )
  @Mapping(
      target = "driverLicense",
      source = "jz"
  )
  @Mapping(
      target = "professionalTitle",
      source = "zc"
  )
  @Mapping(
      target = "startAuxiliaryDate",
      source = "fjgzrq",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "medicalHistory",
      source = "zdbs"
  )
  @Mapping(
      target = "formerName",
      source = "cym"
  )
  @Mapping(
      target = "firstWorkDate",
      source = "scgzsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "partyJoinDate",
      source = "rdsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "laborCompany",
      source = "lwgs"
  )
  @Mapping(
      target = "officePhoneOuter",
      source = "zjWx"
  )
  @Mapping(
      target = "firstAuxiliaryDate",
      source = "scsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "politicalStatus",
      source = "zzmm"
  )
  @Mapping(
      target = "birthDate",
      source = "csrq",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "officePhoneInner",
      source = "zjNx"
  )
  @Mapping(
      target = "phoneNumber",
      source = "sjhm"
  )
  @Mapping(
      target = "healthStatus",
      source = "jkzkmc"
  )
  @Mapping(
      target = "leaderName",
      source = "zrldxm"
  )
  @Mapping(
      target = "leaderPoliceNumber",
      source = "zrldjh"
  )
  @Mapping(
      target = "organization",
      source = "dwmc"
  )
  @Mapping(
      target = "name",
      source = "xm"
  )
  @Mapping(
      target = "nativePlace",
      source = "jgmc"
  )
  @Mapping(
      target = "securityChannel",
      source = "bzqdmc"
  )
  @Mapping(
      target = "position",
      source = "gwmc"
  )
  @Mapping(
      target = "auxiliarySeniority",
      source = "fzgl",
      qualifiedByName = {"bigDecimalToString"}
  )
  @Mapping(
      target = "residenceAddress",
      source = "jzd"
  )
  @Mapping(
      target = "maritalStatus",
      source = "hyzk"
  )
  @Mapping(
      target = "age",
      source = "nl",
      qualifiedByName = {"bigDecimalToString"}
  )
  AuxiliaryPoliceInfo convert(VWjRyjbxxFjxx source, @MappingTarget AuxiliaryPoliceInfo target);
}
