package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceViolationPerson;
import com.hl.archive.domain.entity.PoliceViolationPersonToVWjWgwjdjbRyclMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__557;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__557.class,
    uses = {ConversionUtils.class,PoliceViolationPersonToVWjWgwjdjbRyclMapper.class},
    imports = {}
)
public interface VWjWgwjdjbRyclToPoliceViolationPersonMapper extends BaseMapper<VWjWgwjdjbRycl, PoliceViolationPerson> {
  @Mapping(
      target = "gender",
      source = "xbmc"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "politicalStatus",
      source = "zzmmmc"
  )
  @Mapping(
      target = "caseOrg",
      source = "dwmc"
  )
  @Mapping(
      target = "isAccountability",
      source = "sfdcwzmc"
  )
  @Mapping(
      target = "remark",
      source = "bz"
  )
  @Mapping(
      target = "birthDate",
      source = "csrq",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "wtXxzjbh",
      source = "wtXxzjbh"
  )
  @Mapping(
      target = "xxzjbh",
      source = "xxzjbh"
  )
  @Mapping(
      target = "joinPartyDate",
      source = "rdrq",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "name",
      source = "xm"
  )
  @Mapping(
      target = "rank",
      source = "zjmc"
  )
  @Mapping(
      target = "policeDept",
      source = "jzbmmc"
  )
  @Mapping(
      target = "dispositionCategory",
      source = "clxtmc"
  )
  @Mapping(
      target = "position",
      source = "zwmc"
  )
  PoliceViolationPerson convert(VWjWgwjdjbRycl source);

  @Mapping(
      target = "gender",
      source = "xbmc"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "politicalStatus",
      source = "zzmmmc"
  )
  @Mapping(
      target = "caseOrg",
      source = "dwmc"
  )
  @Mapping(
      target = "isAccountability",
      source = "sfdcwzmc"
  )
  @Mapping(
      target = "remark",
      source = "bz"
  )
  @Mapping(
      target = "birthDate",
      source = "csrq",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "wtXxzjbh",
      source = "wtXxzjbh"
  )
  @Mapping(
      target = "xxzjbh",
      source = "xxzjbh"
  )
  @Mapping(
      target = "joinPartyDate",
      source = "rdrq",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "name",
      source = "xm"
  )
  @Mapping(
      target = "rank",
      source = "zjmc"
  )
  @Mapping(
      target = "policeDept",
      source = "jzbmmc"
  )
  @Mapping(
      target = "dispositionCategory",
      source = "clxtmc"
  )
  @Mapping(
      target = "position",
      source = "zwmc"
  )
  PoliceViolationPerson convert(VWjWgwjdjbRycl source, @MappingTarget PoliceViolationPerson target);
}
