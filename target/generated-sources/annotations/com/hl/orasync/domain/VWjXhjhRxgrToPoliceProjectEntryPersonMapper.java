package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceProjectEntryPerson;
import com.hl.archive.domain.entity.PoliceProjectEntryPersonToVWjXhjhRxgrMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__555;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__555.class,
    uses = {ConversionUtils.class,PoliceProjectEntryPersonToVWjXhjhRxgrMapper.class},
    imports = {}
)
public interface VWjXhjhRxgrToPoliceProjectEntryPersonMapper extends BaseMapper<VWjXhjhRxgr, PoliceProjectEntryPerson> {
  @Mapping(
      target = "policeNumber",
      source = "jh"
  )
  @Mapping(
      target = "entryTime",
      source = "djsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "unit",
      source = "dwmc"
  )
  @Mapping(
      target = "honorLevel",
      source = "ryjbmc"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "honorName",
      source = "zgrymc"
  )
  @Mapping(
      target = "name",
      source = "xm"
  )
  @Mapping(
      target = "auditStatus",
      source = "shztmc"
  )
  @Mapping(
      target = "contactPerson",
      source = "pylxrXm"
  )
  @Mapping(
      target = "zjbh",
      source = "xxzjbh"
  )
  PoliceProjectEntryPerson convert(VWjXhjhRxgr source);

  @Mapping(
      target = "policeNumber",
      source = "jh"
  )
  @Mapping(
      target = "entryTime",
      source = "djsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "unit",
      source = "dwmc"
  )
  @Mapping(
      target = "honorLevel",
      source = "ryjbmc"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "honorName",
      source = "zgrymc"
  )
  @Mapping(
      target = "name",
      source = "xm"
  )
  @Mapping(
      target = "auditStatus",
      source = "shztmc"
  )
  @Mapping(
      target = "contactPerson",
      source = "pylxrXm"
  )
  @Mapping(
      target = "zjbh",
      source = "xxzjbh"
  )
  PoliceProjectEntryPerson convert(VWjXhjhRxgr source,
      @MappingTarget PoliceProjectEntryPerson target);
}
