package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceQuarterlyAssessment;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-18T13:47:24+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjRyjdkhToPoliceQuarterlyAssessmentMapperImpl implements VWjRyjdkhToPoliceQuarterlyAssessmentMapper {

    @Override
    public PoliceQuarterlyAssessment convert(VWjRyjdkh source) {
        if ( source == null ) {
            return null;
        }

        PoliceQuarterlyAssessment policeQuarterlyAssessment = new PoliceQuarterlyAssessment();

        policeQuarterlyAssessment.setAssessmentResult( source.getJgmc() );
        policeQuarterlyAssessment.setIdCard( source.getGmsfhm() );
        policeQuarterlyAssessment.setAssessmentUnit( source.getDwmc() );
        policeQuarterlyAssessment.setAssessmentQuarter( source.getJd() );
        policeQuarterlyAssessment.setAssessmentYear( source.getNd() );

        return policeQuarterlyAssessment;
    }

    @Override
    public PoliceQuarterlyAssessment convert(VWjRyjdkh source, PoliceQuarterlyAssessment target) {
        if ( source == null ) {
            return target;
        }

        target.setAssessmentResult( source.getJgmc() );
        target.setIdCard( source.getGmsfhm() );
        target.setAssessmentUnit( source.getDwmc() );
        target.setAssessmentQuarter( source.getJd() );
        target.setAssessmentYear( source.getNd() );

        return target;
    }
}
