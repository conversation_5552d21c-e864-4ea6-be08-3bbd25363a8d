package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceChildrenForeignMarriage;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-18T13:47:25+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjZnGwthToPoliceChildrenForeignMarriageMapperImpl implements VWjZnGwthToPoliceChildrenForeignMarriageMapper {

    @Override
    public PoliceChildrenForeignMarriage convert(VWjZnGwth source) {
        if ( source == null ) {
            return null;
        }

        PoliceChildrenForeignMarriage policeChildrenForeignMarriage = new PoliceChildrenForeignMarriage();

        policeChildrenForeignMarriage.setSpouseCountry( source.getGjZnpo() );
        policeChildrenForeignMarriage.setIdCard( source.getGmsfhm() );
        policeChildrenForeignMarriage.setChildName( source.getXmZn() );
        policeChildrenForeignMarriage.setRegistrationDate( ConversionUtils.strToDate( source.getDjrq() ) );
        policeChildrenForeignMarriage.setPosition( source.getZwZnpo() );
        policeChildrenForeignMarriage.setSpouseName( source.getXmZnpo() );
        policeChildrenForeignMarriage.setWorkStudyUnit( source.getGzdwZnpo() );

        return policeChildrenForeignMarriage;
    }

    @Override
    public PoliceChildrenForeignMarriage convert(VWjZnGwth source, PoliceChildrenForeignMarriage target) {
        if ( source == null ) {
            return target;
        }

        target.setSpouseCountry( source.getGjZnpo() );
        target.setIdCard( source.getGmsfhm() );
        target.setChildName( source.getXmZn() );
        target.setRegistrationDate( ConversionUtils.strToDate( source.getDjrq() ) );
        target.setPosition( source.getZwZnpo() );
        target.setSpouseName( source.getXmZnpo() );
        target.setWorkStudyUnit( source.getGzdwZnpo() );

        return target;
    }
}
