package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceLoanInfo;
import com.hl.archive.domain.entity.PoliceLoanInfoToVWjBrGrjdMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__555;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__555.class,
    uses = {ConversionUtils.class,PoliceLoanInfoToVWjBrGrjdMapper.class},
    imports = {}
)
public interface VWjBrGrjdToPoliceLoanInfoMapper extends BaseMapper<VWjBrGrjd, PoliceLoanInfo> {
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "loanInfo",
      source = "jdlxmc"
  )
  @Mapping(
      target = "lenderName",
      source = "xmJddx"
  )
  @Mapping(
      target = "loanDate",
      source = "jdrq",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "loanPurpose",
      source = "jdyt"
  )
  @Mapping(
      target = "repaymentDeadline",
      source = "hkqx",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "loanAmount",
      source = "je",
      qualifiedByName = {"strToBigDecimal"}
  )
  PoliceLoanInfo convert(VWjBrGrjd source);

  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "loanInfo",
      source = "jdlxmc"
  )
  @Mapping(
      target = "lenderName",
      source = "xmJddx"
  )
  @Mapping(
      target = "loanDate",
      source = "jdrq",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "loanPurpose",
      source = "jdyt"
  )
  @Mapping(
      target = "repaymentDeadline",
      source = "hkqx",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "loanAmount",
      source = "je",
      qualifiedByName = {"strToBigDecimal"}
  )
  PoliceLoanInfo convert(VWjBrGrjd source, @MappingTarget PoliceLoanInfo target);
}
