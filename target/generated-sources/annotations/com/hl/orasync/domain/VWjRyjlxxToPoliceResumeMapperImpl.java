package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceResume;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-18T14:33:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjRyjlxxToPoliceResumeMapperImpl implements VWjRyjlxxToPoliceResumeMapper {

    @Override
    public PoliceResume convert(VWjRyjlxx source) {
        if ( source == null ) {
            return null;
        }

        PoliceResume policeResume = new PoliceResume();

        policeResume.setWorkUnit( source.getSzdw() );
        policeResume.setEndDate( ConversionUtils.strToDate( source.getJzsj() ) );
        policeResume.setIdCard( source.getGmsfhm() );
        policeResume.setPosition( source.getZw() );
        policeResume.setStartDate( ConversionUtils.strToDate( source.getQssj() ) );

        return policeResume;
    }

    @Override
    public PoliceResume convert(VWjRyjlxx source, PoliceResume target) {
        if ( source == null ) {
            return target;
        }

        target.setWorkUnit( source.getSzdw() );
        target.setEndDate( ConversionUtils.strToDate( source.getJzsj() ) );
        target.setIdCard( source.getGmsfhm() );
        target.setPosition( source.getZw() );
        target.setStartDate( ConversionUtils.strToDate( source.getQssj() ) );

        return target;
    }
}
