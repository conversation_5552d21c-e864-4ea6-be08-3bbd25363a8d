package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceProjectContact;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-18T13:47:25+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjXhjhRxgrPylxToPoliceProjectContactMapperImpl implements VWjXhjhRxgrPylxToPoliceProjectContactMapper {

    @Override
    public PoliceProjectContact convert(VWjXhjhRxgrPylx source) {
        if ( source == null ) {
            return null;
        }

        PoliceProjectContact policeProjectContact = new PoliceProjectContact();

        policeProjectContact.setEvaluation( source.getPypj() );
        policeProjectContact.setTraits( source.getTzld() );
        policeProjectContact.setRegisteredBy( source.getDjrxm() );
        policeProjectContact.setRegisterTime( ConversionUtils.strToDate( source.getDjsj() ) );
        policeProjectContact.setContactName( source.getPylxrXm() );
        policeProjectContact.setContactPosition( source.getPylxrZw() );
        policeProjectContact.setXhjhZjbh( source.getJlXxzjbh() );
        policeProjectContact.setZjbh( source.getXxzjbh() );

        return policeProjectContact;
    }

    @Override
    public PoliceProjectContact convert(VWjXhjhRxgrPylx source, PoliceProjectContact target) {
        if ( source == null ) {
            return target;
        }

        target.setEvaluation( source.getPypj() );
        target.setTraits( source.getTzld() );
        target.setRegisteredBy( source.getDjrxm() );
        target.setRegisterTime( ConversionUtils.strToDate( source.getDjsj() ) );
        target.setContactName( source.getPylxrXm() );
        target.setContactPosition( source.getPylxrZw() );
        target.setXhjhZjbh( source.getJlXxzjbh() );
        target.setZjbh( source.getXxzjbh() );

        return target;
    }
}
