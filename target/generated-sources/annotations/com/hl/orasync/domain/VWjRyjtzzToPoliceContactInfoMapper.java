package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceContactInfo;
import com.hl.archive.domain.entity.PoliceContactInfoToVWjRyjtzzMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__555;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__555.class,
    uses = {ConversionUtils.class,PoliceContactInfoToVWjRyjtzzMapper.class},
    imports = {}
)
public interface VWjRyjtzzToPoliceContactInfoMapper extends BaseMapper<VWjRyjtzz, PoliceContactInfo> {
  @Mapping(
      target = "mobilePhone",
      source = "shhm"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "mobileShortNumber",
      source = "shhmdh"
  )
  @Mapping(
      target = "homePhone",
      source = "jtdh"
  )
  @Mapping(
      target = "homeAddress",
      source = "jtdz"
  )
  PoliceContactInfo convert(VWjRyjtzz source);

  @Mapping(
      target = "mobilePhone",
      source = "shhm"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "mobileShortNumber",
      source = "shhmdh"
  )
  @Mapping(
      target = "homePhone",
      source = "jtdh"
  )
  @Mapping(
      target = "homeAddress",
      source = "jtdz"
  )
  PoliceContactInfo convert(VWjRyjtzz source, @MappingTarget PoliceContactInfo target);
}
