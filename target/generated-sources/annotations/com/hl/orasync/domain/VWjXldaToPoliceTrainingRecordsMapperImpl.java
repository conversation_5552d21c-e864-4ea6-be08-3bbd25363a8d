package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceTrainingRecords;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-18T14:33:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjXldaToPoliceTrainingRecordsMapperImpl implements VWjXldaToPoliceTrainingRecordsMapper {

    @Override
    public PoliceTrainingRecords convert(VWjXlda source) {
        if ( source == null ) {
            return null;
        }

        PoliceTrainingRecords policeTrainingRecords = new PoliceTrainingRecords();

        policeTrainingRecords.setScore( source.getPfdf() );
        if ( source.getPxsj() != null ) {
            policeTrainingRecords.setTrainingTime( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getPxsj() ) );
        }
        policeTrainingRecords.setExamProjectName( source.getKpxmmc() );
        policeTrainingRecords.setTrainingName( source.getPxbmc() );
        policeTrainingRecords.setIdCard( source.getGmsfhm() );
        policeTrainingRecords.setGrade( source.getCj() );

        return policeTrainingRecords;
    }

    @Override
    public PoliceTrainingRecords convert(VWjXlda source, PoliceTrainingRecords target) {
        if ( source == null ) {
            return target;
        }

        target.setScore( source.getPfdf() );
        if ( source.getPxsj() != null ) {
            target.setTrainingTime( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getPxsj() ) );
        }
        else {
            target.setTrainingTime( null );
        }
        target.setExamProjectName( source.getKpxmmc() );
        target.setTrainingName( source.getPxbmc() );
        target.setIdCard( source.getGmsfhm() );
        target.setGrade( source.getCj() );

        return target;
    }
}
