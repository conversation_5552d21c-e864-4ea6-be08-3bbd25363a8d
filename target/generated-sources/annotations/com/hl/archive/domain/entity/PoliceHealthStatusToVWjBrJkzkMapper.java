package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBrJkzk;
import com.hl.orasync.domain.VWjBrJkzkToPoliceHealthStatusMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__557;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__557.class,
    uses = {ConversionUtils.class,VWjBrJkzkToPoliceHealthStatusMapper.class},
    imports = {}
)
public interface PoliceHealthStatusToVWjBrJkzkMapper extends BaseMapper<PoliceHealthStatus, VWjBrJkzk> {
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "jbmc",
      source = "illnessName"
  )
  @Mapping(
      target = "zysj",
      source = "diagnosisDate"
  )
  VWjBrJkzk convert(PoliceHealthStatus source);

  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "jbmc",
      source = "illnessName"
  )
  @Mapping(
      target = "zysj",
      source = "diagnosisDate"
  )
  VWjBrJkzk convert(PoliceHealthStatus source, @MappingTarget VWjBrJkzk target);
}
