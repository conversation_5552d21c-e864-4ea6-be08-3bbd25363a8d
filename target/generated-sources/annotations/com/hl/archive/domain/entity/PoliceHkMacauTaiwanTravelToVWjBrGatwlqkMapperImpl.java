package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBrGatwlqk;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-18T14:33:48+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceHkMacauTaiwanTravelToVWjBrGatwlqkMapperImpl implements PoliceHkMacauTaiwanTravelToVWjBrGatwlqkMapper {

    @Override
    public VWjBrGatwlqk convert(PoliceHkMacauTaiwanTravel source) {
        if ( source == null ) {
            return null;
        }

        VWjBrGatwlqk vWjBrGatwlqk = new VWjBrGatwlqk();

        if ( source.getEndDate() != null ) {
            vWjBrGatwlqk.setJssj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getEndDate() ) );
        }
        vWjBrGatwlqk.setSdgj( source.getDestinationRegion() );
        vWjBrGatwlqk.setSy( source.getTravelReason() );
        vWjBrGatwlqk.setGmsfhm( source.getIdCard() );
        vWjBrGatwlqk.setSpjgmc( source.getApprovalAuthority() );
        vWjBrGatwlqk.setZjhm( source.getDocumentNumber() );
        if ( source.getStartDate() != null ) {
            vWjBrGatwlqk.setKssj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getStartDate() ) );
        }

        return vWjBrGatwlqk;
    }

    @Override
    public VWjBrGatwlqk convert(PoliceHkMacauTaiwanTravel source, VWjBrGatwlqk target) {
        if ( source == null ) {
            return target;
        }

        if ( source.getEndDate() != null ) {
            target.setJssj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getEndDate() ) );
        }
        else {
            target.setJssj( null );
        }
        target.setSdgj( source.getDestinationRegion() );
        target.setSy( source.getTravelReason() );
        target.setGmsfhm( source.getIdCard() );
        target.setSpjgmc( source.getApprovalAuthority() );
        target.setZjhm( source.getDocumentNumber() );
        if ( source.getStartDate() != null ) {
            target.setKssj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getStartDate() ) );
        }
        else {
            target.setKssj( null );
        }

        return target;
    }
}
