package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRytc;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-18T14:33:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceSpecialtiesToVWjRytcMapperImpl implements PoliceSpecialtiesToVWjRytcMapper {

    @Override
    public VWjRytc convert(PoliceSpecialties source) {
        if ( source == null ) {
            return null;
        }

        VWjRytc vWjRytc = new VWjRytc();

        if ( source.getAwardDate() != null ) {
            vWjRytc.setJcsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getAwardDate() ) );
        }
        vWjRytc.setGmsfhm( source.getIdCard() );
        vWjRytc.setJcpjjgmc( source.getApproveAuthority() );
        vWjRytc.setJcmc( source.getSpecialtyName() );

        return vWjRytc;
    }

    @Override
    public VWjRytc convert(PoliceSpecialties source, VWjRytc target) {
        if ( source == null ) {
            return target;
        }

        if ( source.getAwardDate() != null ) {
            target.setJcsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getAwardDate() ) );
        }
        else {
            target.setJcsj( null );
        }
        target.setGmsfhm( source.getIdCard() );
        target.setJcpjjgmc( source.getApproveAuthority() );
        target.setJcmc( source.getSpecialtyName() );

        return target;
    }
}
