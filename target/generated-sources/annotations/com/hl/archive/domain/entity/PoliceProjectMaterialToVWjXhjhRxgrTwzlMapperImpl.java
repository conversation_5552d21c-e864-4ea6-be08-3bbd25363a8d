package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjXhjhRxgrTwzl;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-18T13:47:24+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceProjectMaterialToVWjXhjhRxgrTwzlMapperImpl implements PoliceProjectMaterialToVWjXhjhRxgrTwzlMapper {

    @Override
    public VWjXhjhRxgrTwzl convert(PoliceProjectMaterial source) {
        if ( source == null ) {
            return null;
        }

        VWjXhjhRxgrTwzl vWjXhjhRxgrTwzl = new VWjXhjhRxgrTwzl();

        vWjXhjhRxgrTwzl.setJlXxzjbh( source.getXhjhZjbh() );
        vWjXhjhRxgrTwzl.setTpzlBt( source.getImageName() );
        vWjXhjhRxgrTwzl.setTpzlNr( source.getImageUrl() );
        vWjXhjhRxgrTwzl.setXxzjbh( source.getZjbh() );

        return vWjXhjhRxgrTwzl;
    }

    @Override
    public VWjXhjhRxgrTwzl convert(PoliceProjectMaterial source, VWjXhjhRxgrTwzl target) {
        if ( source == null ) {
            return target;
        }

        target.setJlXxzjbh( source.getXhjhZjbh() );
        target.setTpzlBt( source.getImageName() );
        target.setTpzlNr( source.getImageUrl() );
        target.setXxzjbh( source.getZjbh() );

        return target;
    }
}
