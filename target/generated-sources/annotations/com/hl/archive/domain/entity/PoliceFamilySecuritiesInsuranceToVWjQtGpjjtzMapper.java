package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjQtGpjjtz;
import com.hl.orasync.domain.VWjQtGpjjtzToPoliceFamilySecuritiesInsuranceMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__557;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__557.class,
    uses = {ConversionUtils.class,VWjQtGpjjtzToPoliceFamilySecuritiesInsuranceMapper.class},
    imports = {}
)
public interface PoliceFamilySecuritiesInsuranceToVWjQtGpjjtzMapper extends BaseMapper<PoliceFamilySecuritiesInsurance, VWjQtGpjjtz> {
  @Mapping(
      target = "rjz",
      source = "netValuePremium"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "sl",
      source = "holdingQuantity"
  )
  @Mapping(
      target = "xmCyr",
      source = "holderName"
  )
  @Mapping(
      target = "mcdm",
      source = "securityNameCode"
  )
  VWjQtGpjjtz convert(PoliceFamilySecuritiesInsurance source);

  @Mapping(
      target = "rjz",
      source = "netValuePremium"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "sl",
      source = "holdingQuantity"
  )
  @Mapping(
      target = "xmCyr",
      source = "holderName"
  )
  @Mapping(
      target = "mcdm",
      source = "securityNameCode"
  )
  VWjQtGpjjtz convert(PoliceFamilySecuritiesInsurance source, @MappingTarget VWjQtGpjjtz target);
}
