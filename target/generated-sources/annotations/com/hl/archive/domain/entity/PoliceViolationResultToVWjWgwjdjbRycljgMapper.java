package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjWgwjdjbRycljg;
import com.hl.orasync.domain.VWjWgwjdjbRycljgToPoliceViolationResultMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__557;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__557.class,
    uses = {ConversionUtils.class,VWjWgwjdjbRycljgToPoliceViolationResultMapper.class},
    imports = {}
)
public interface PoliceViolationResultToVWjWgwjdjbRycljgMapper extends BaseMapper<PoliceViolationResult, VWjWgwjdjbRycljg> {
  @Mapping(
      target = "cldw",
      source = "cldw"
  )
  @Mapping(
      target = "cljg",
      source = "cljg"
  )
  @Mapping(
      target = "ryXxzjbh",
      source = "ryXxzjbh"
  )
  @Mapping(
      target = "wtXxzjbh",
      source = "wtXxzjbh"
  )
  @Mapping(
      target = "lbmc",
      source = "lbmc"
  )
  @Mapping(
      target = "clsj",
      source = "clsj"
  )
  @Mapping(
      target = "xxzjbh",
      source = "xxzjbh"
  )
  VWjWgwjdjbRycljg convert(PoliceViolationResult source);

  @Mapping(
      target = "cldw",
      source = "cldw"
  )
  @Mapping(
      target = "cljg",
      source = "cljg"
  )
  @Mapping(
      target = "ryXxzjbh",
      source = "ryXxzjbh"
  )
  @Mapping(
      target = "wtXxzjbh",
      source = "wtXxzjbh"
  )
  @Mapping(
      target = "lbmc",
      source = "lbmc"
  )
  @Mapping(
      target = "clsj",
      source = "clsj"
  )
  @Mapping(
      target = "xxzjbh",
      source = "xxzjbh"
  )
  VWjWgwjdjbRycljg convert(PoliceViolationResult source, @MappingTarget VWjWgwjdjbRycljg target);
}
