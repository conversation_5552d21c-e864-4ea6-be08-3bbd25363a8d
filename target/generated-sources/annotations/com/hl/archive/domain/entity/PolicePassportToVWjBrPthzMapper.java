package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBrPthz;
import com.hl.orasync.domain.VWjBrPthzToPolicePassportMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__557;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__557.class,
    uses = {ConversionUtils.class,VWjBrPthzToPolicePassportMapper.class},
    imports = {}
)
public interface PolicePassportToVWjBrPthzMapper extends BaseMapper<PolicePassport, VWjBrPthz> {
  @Mapping(
      target = "bgjgmc",
      source = "custodyOrganization"
  )
  @Mapping(
      target = "qfrq",
      source = "issueDate"
  )
  @Mapping(
      target = "yxqz",
      source = "expiryDate"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "hzhm",
      source = "passportNumber"
  )
  @Mapping(
      target = "bz",
      source = "remarks"
  )
  VWjBrPthz convert(PolicePassport source);

  @Mapping(
      target = "bgjgmc",
      source = "custodyOrganization"
  )
  @Mapping(
      target = "qfrq",
      source = "issueDate"
  )
  @Mapping(
      target = "yxqz",
      source = "expiryDate"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "hzhm",
      source = "passportNumber"
  )
  @Mapping(
      target = "bz",
      source = "remarks"
  )
  VWjBrPthz convert(PolicePassport source, @MappingTarget VWjBrPthz target);
}
