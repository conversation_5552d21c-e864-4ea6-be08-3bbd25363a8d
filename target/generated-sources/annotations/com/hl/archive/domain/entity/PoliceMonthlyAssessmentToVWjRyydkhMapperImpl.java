package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyydkh;
import java.math.BigDecimal;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-18T13:47:24+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceMonthlyAssessmentToVWjRyydkhMapperImpl implements PoliceMonthlyAssessmentToVWjRyydkhMapper {

    @Override
    public VWjRyydkh convert(PoliceMonthlyAssessment source) {
        if ( source == null ) {
            return null;
        }

        VWjRyydkh vWjRyydkh = new VWjRyydkh();

        if ( source.getAssessmentBonus() != null ) {
            vWjRyydkh.setJj( new BigDecimal( source.getAssessmentBonus() ) );
        }
        vWjRyydkh.setKptbpzmc( source.getAssessmentNotice() );
        if ( source.getAssessmentScore() != null ) {
            vWjRyydkh.setDf( new BigDecimal( source.getAssessmentScore() ) );
        }
        vWjRyydkh.setNd( source.getAssessmentYear() );
        vWjRyydkh.setGmsfhm( source.getIdCard() );
        if ( source.getAssessmentRanking() != null ) {
            vWjRyydkh.setPm( new BigDecimal( source.getAssessmentRanking() ) );
        }
        vWjRyydkh.setYf( source.getAssessmentMonth() );

        return vWjRyydkh;
    }

    @Override
    public VWjRyydkh convert(PoliceMonthlyAssessment source, VWjRyydkh target) {
        if ( source == null ) {
            return target;
        }

        if ( source.getAssessmentBonus() != null ) {
            target.setJj( new BigDecimal( source.getAssessmentBonus() ) );
        }
        else {
            target.setJj( null );
        }
        target.setKptbpzmc( source.getAssessmentNotice() );
        if ( source.getAssessmentScore() != null ) {
            target.setDf( new BigDecimal( source.getAssessmentScore() ) );
        }
        else {
            target.setDf( null );
        }
        target.setNd( source.getAssessmentYear() );
        target.setGmsfhm( source.getIdCard() );
        if ( source.getAssessmentRanking() != null ) {
            target.setPm( new BigDecimal( source.getAssessmentRanking() ) );
        }
        else {
            target.setPm( null );
        }
        target.setYf( source.getAssessmentMonth() );

        return target;
    }
}
