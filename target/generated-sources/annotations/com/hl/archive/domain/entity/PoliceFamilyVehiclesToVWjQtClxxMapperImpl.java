package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjQtClxx;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-18T13:47:25+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceFamilyVehiclesToVWjQtClxxMapperImpl implements PoliceFamilyVehiclesToVWjQtClxxMapper {

    @Override
    public VWjQtClxx convert(PoliceFamilyVehicles source) {
        if ( source == null ) {
            return null;
        }

        VWjQtClxx vWjQtClxx = new VWjQtClxx();

        vWjQtClxx.setClqxmc( source.getVehicleDisposition() );
        vWjQtClxx.setXmCqr( source.getOwnerName() );
        if ( source.getSaleAmount() != null ) {
            vWjQtClxx.setCsjg( source.getSaleAmount().toString() );
        }
        vWjQtClxx.setGmsfhm( source.getIdCard() );
        vWjQtClxx.setClpp( source.getVehicleBrand() );
        vWjQtClxx.setCllymc( source.getVehicleSource() );
        if ( source.getSaleDate() != null ) {
            vWjQtClxx.setCssj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getSaleDate() ) );
        }
        if ( source.getTransactionAmount() != null ) {
            vWjQtClxx.setJe( source.getTransactionAmount().toString() );
        }
        if ( source.getTransactionDate() != null ) {
            vWjQtClxx.setGmsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getTransactionDate() ) );
        }
        vWjQtClxx.setHphm( source.getLicensePlate() );

        return vWjQtClxx;
    }

    @Override
    public VWjQtClxx convert(PoliceFamilyVehicles source, VWjQtClxx target) {
        if ( source == null ) {
            return target;
        }

        target.setClqxmc( source.getVehicleDisposition() );
        target.setXmCqr( source.getOwnerName() );
        if ( source.getSaleAmount() != null ) {
            target.setCsjg( source.getSaleAmount().toString() );
        }
        else {
            target.setCsjg( null );
        }
        target.setGmsfhm( source.getIdCard() );
        target.setClpp( source.getVehicleBrand() );
        target.setCllymc( source.getVehicleSource() );
        if ( source.getSaleDate() != null ) {
            target.setCssj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getSaleDate() ) );
        }
        else {
            target.setCssj( null );
        }
        if ( source.getTransactionAmount() != null ) {
            target.setJe( source.getTransactionAmount().toString() );
        }
        else {
            target.setJe( null );
        }
        if ( source.getTransactionDate() != null ) {
            target.setGmsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getTransactionDate() ) );
        }
        else {
            target.setGmsj( null );
        }
        target.setHphm( source.getLicensePlate() );

        return target;
    }
}
