2025-09-18 14:19:02.371 [36mINFO [m [35m[main][m [36mc.a.n.c.e.SearchableProperties[m [34m[][m - [32mproperties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[m2025-09-18 14:19:02.437 [36mINFO [m [35m[background-preinit][m [36mo.h.v.i.u.Version[m [34m[][m - [32mHV000001: Hibernate Validator 6.2.5.Final
[m2025-09-18 14:19:05.346 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 14:19:05.346 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 14:19:09.241 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-18 14:19:09.250 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-18 14:19:09.256 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-18 14:19:09.258 [36mINFO [m [35m[main][m [36mo.s.c.b.c.PropertySourceBootstrapConfiguration[m [34m[][m - [32mLocated property source: [BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive-druid.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive,default'}]
[m2025-09-18 14:19:09.305 [36mINFO [m [35m[main][m [36mc.h.AppMain[m [34m[][m - [32mThe following 1 profile is active: "druid"
[m2025-09-18 14:19:12.666 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mMultiple Spring Data modules found, entering strict repository configuration mode
[m2025-09-18 14:19:12.678 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
[m2025-09-18 14:19:12.784 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 74 ms. Found 0 Elasticsearch repository interfaces.
[m2025-09-18 14:19:12.792 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mMultiple Spring Data modules found, entering strict repository configuration mode
[m2025-09-18 14:19:12.795 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
[m2025-09-18 14:19:12.834 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 39 ms. Found 0 Reactive Elasticsearch repository interfaces.
[m2025-09-18 14:19:12.858 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mMultiple Spring Data modules found, entering strict repository configuration mode
[m2025-09-18 14:19:12.862 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Redis repositories in DEFAULT mode.
[m2025-09-18 14:19:12.918 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 40 ms. Found 0 Redis repository interfaces.
[m2025-09-18 14:19:13.624 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.624 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.625 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.625 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.625 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.625 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.626 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.626 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.626 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.626 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:14.652 [36mINFO [m [35m[main][m [36mo.s.c.c.s.GenericScope[m [34m[][m - [32mBeanFactory id=f26d4248-e3fc-381f-8478-184d97d56f2e
[m2025-09-18 14:19:16.334 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 14:19:16.345 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 14:19:16.348 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$597/258548151] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 14:19:16.365 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 14:19:16.694 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'dictProperties' of type [com.hl.dict.config.DictProperties$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 14:19:16.702 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'dynamicTableNameHandler' of type [com.hl.dict.config.DynamicTableNameHandler$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 14:19:18.036 [36mINFO [m [35m[main][m [36mo.s.b.w.e.t.TomcatWebServer[m [34m[][m - [32mTomcat initialized with port(s): 28183 (http)
[m2025-09-18 14:19:18.117 [36mINFO [m [35m[main][m [36mo.a.c.h.Http11NioProtocol[m [34m[][m - [32mInitializing ProtocolHandler ["http-nio-28183"]
[m2025-09-18 14:19:18.124 [36mINFO [m [35m[main][m [36mo.a.c.c.StandardService[m [34m[][m - [32mStarting service [Tomcat]
[m2025-09-18 14:19:18.124 [36mINFO [m [35m[main][m [36mo.a.c.c.StandardEngine[m [34m[][m - [32mStarting Servlet engine: [Apache Tomcat/9.0.83]
[m2025-09-18 14:19:18.567 [36mINFO [m [35m[main][m [36mo.a.c.c.C.[.[.[/][m [34m[][m - [32mInitializing Spring embedded WebApplicationContext
[m2025-09-18 14:19:18.567 [36mINFO [m [35m[main][m [36mo.s.b.w.s.c.ServletWebServerApplicationContext[m [34m[][m - [32mRoot WebApplicationContext: initialization completed in 9233 ms
[m2025-09-18 14:19:19.128 [36mINFO [m [35m[main][m [36mo.s.c.o.FeignClientFactoryBean[m [34m[][m - [32mFor 'sso-hl' URL not provided. Will try picking an instance via load-balancing.
[m2025-09-18 14:19:20.213 [36mINFO [m [35m[main][m [36mc.h.d.c.MybatisPlusEnhancerConfiguration[m [34m[][m - [32m✅ Enhanced MybatisPlusInterceptor with DynamicTableNameInnerInterceptor.
[m2025-09-18 14:19:24.486 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-18 14:19:24.487 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.505 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-18 14:19:24.506 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.523 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-18 14:19:24.523 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.541 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-18 14:19:24.542 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.557 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-18 14:19:24.557 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.574 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-18 14:19:24.575 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.593 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-18 14:19:24.593 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.609 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-18 14:19:24.609 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.625 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-18 14:19:24.626 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.643 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-18 14:19:24.644 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.662 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-18 14:19:24.663 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.681 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-18 14:19:24.682 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.700 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-18 14:19:24.736 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-18 14:19:24.737 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.760 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-18 14:19:24.760 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.778 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-18 14:19:24.779 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.799 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjLzfx".
[m2025-09-18 14:19:24.800 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjLzfx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.816 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-18 14:19:24.817 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.836 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-18 14:19:24.836 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.856 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-18 14:19:24.857 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.878 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-18 14:19:24.879 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.898 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-18 14:19:24.898 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.915 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-18 14:19:24.916 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.932 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-18 14:19:24.932 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.948 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-18 14:19:24.949 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.001 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-18 14:19:25.001 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.029 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-18 14:19:25.029 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.046 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-18 14:19:25.046 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.062 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-18 14:19:25.062 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.078 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-18 14:19:25.080 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.095 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-18 14:19:25.095 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.114 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-18 14:19:25.114 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.130 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-18 14:19:25.130 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.145 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-18 14:19:25.146 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.164 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-18 14:19:25.164 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.180 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-18 14:19:25.181 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.197 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-18 14:19:25.198 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.216 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-18 14:19:25.216 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.228 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-18 14:19:25.230 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.257 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-18 14:19:25.257 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.280 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-18 14:19:25.281 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.297 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-18 14:19:25.297 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.316 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-18 14:19:25.316 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.335 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-18 14:19:25.336 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.354 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-18 14:19:25.355 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.372 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-18 14:19:25.372 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.389 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-18 14:19:25.389 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.406 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-18 14:19:25.406 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.428 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjYjbb".
[m2025-09-18 14:19:25.428 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjYjbb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.446 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-18 14:19:25.447 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.475 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-18 14:19:25.475 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.507 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-18 14:19:25.508 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.539 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-18 14:19:25.539 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.559 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-18 14:19:25.559 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.578 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-18 14:19:25.578 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:26.724 [36mINFO [m [35m[main][m [36mo.s.c.o.FeignClientFactoryBean[m [34m[][m - [32mFor 'hl-task' URL not provided. Will try picking an instance via load-balancing.
[m2025-09-18 14:19:43.283 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:19:46.845 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:19:46.846 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:19:46.966 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:19:50.001 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:19:50.002 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:19:50.144 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:19:53.179 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:19:53.179 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:19:53.316 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:19:56.365 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:19:56.365 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:19:56.508 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:19:59.542 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:19:59.542 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:19:59.683 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:20:02.719 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:20:02.719 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:20:02.866 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:20:05.905 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:20:05.906 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:20:06.042 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:20:09.261 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:20:09.262 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:20:09.762 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CG9Z9HJ3FJW -> BusinessTripStrategy
[m2025-09-18 14:20:09.762 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: C09ZZEL29S8 -> LeaveChangZhouStrategy
[m2025-09-18 14:20:09.762 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CC42ITDEHRQ -> LeaveRequestStrategy
[m2025-09-18 14:20:09.763 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CKSWWQQWW7H -> OverseasTravelStrategy
[m2025-09-18 14:20:09.763 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CNE2TZD2GTE -> PoliceAbilityStrategy
[m2025-09-18 14:20:09.763 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CXQNJMIAR1C -> PoliceClubActivityStrategy
[m2025-09-18 14:20:09.763 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: C30NIBEJEGI -> PoliceDrinkReportStrategy
[m2025-09-18 14:20:09.978 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:20:13.021 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:20:13.021 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:20:13.400 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:20:16.428 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:20:16.430 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:20:34.914 [36mINFO [m [35m[main][m [36mo.s.c.c.u.InetUtils[m [34m[][m - [32mCannot determine local hostname
[m2025-09-18 14:20:35.263 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 14:20:35.264 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 14:20:36.384 [36mINFO [m [35m[main][m [36mc.h.n.c.SharedNacosNameSpace[m [34m[][m - [32m{"secretKey":"","password":"hl123","namespace":"public","accessKey":"","serverAddr":"**************:8848","isUseCloudNamespaceParsing":"false","clusterName":"","username":"nacos"}
[m2025-09-18 14:20:37.625 [36mINFO [m [35m[main][m [36mo.s.c.c.u.InetUtils[m [34m[][m - [32mCannot determine local hostname
[m2025-09-18 14:20:38.809 [36mINFO [m [35m[main][m [36mo.s.c.o.FeignClientFactoryBean[m [34m[][m - [32mFor 'hl-dict' URL not provided. Will try picking an instance via load-balancing.
[m2025-09-18 14:20:44.793 [36mINFO [m [35m[main][m [36mo.s.b.a.e.w.EndpointLinksResolver[m [34m[][m - [32mExposing 1 endpoint(s) beneath base path '/actuator'
[m2025-09-18 14:20:46.624 [36mINFO [m [35m[main][m [36mc.h.s.c.SecurityConfig[m [34m[][m - [32m[/error/logs, /test/dict, /dict/api-query, /dict-test/test, /dict/add-batch] --> 200
[m2025-09-18 14:20:47.049 [36mINFO [m [35m[main][m [36mo.s.s.w.DefaultSecurityFilterChain[m [34m[][m - [32mWill secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@61b121e4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@76d7ec52, org.springframework.security.web.context.SecurityContextPersistenceFilter@46b27d1, org.springframework.security.web.header.HeaderWriterFilter@7fe3cd91, org.springframework.security.web.authentication.logout.LogoutFilter@67e206c5, org.springframework.web.filter.CorsFilter@518990a6, com.hl.security.config.sso.SsoAuthTokenFilter@2c5dfc9b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@66881001, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5e59113f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4b4ac58d, org.springframework.security.web.session.SessionManagementFilter@574b1fb6, org.springframework.security.web.access.ExceptionTranslationFilter@65e1e974, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4b8bc280]
[m2025-09-18 14:20:54.982 [36mINFO [m [35m[main][m [36mo.a.c.h.Http11NioProtocol[m [34m[][m - [32mStarting ProtocolHandler ["http-nio-28183"]
[m2025-09-18 14:20:55.137 [36mINFO [m [35m[main][m [36mo.s.b.w.e.t.TomcatWebServer[m [34m[][m - [32mTomcat started on port(s): 28183 (http) with context path ''
[m2025-09-18 14:20:56.358 [36mINFO [m [35m[main][m [36mo.s.c.c.u.InetUtils[m [34m[][m - [32mCannot determine local hostname
[m2025-09-18 14:20:56.369 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 14:20:56.369 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 14:20:56.635 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosServiceRegistry[m [34m[][m - [32mnacos registry, default hl-wj-police-archive *************:28183 register finished
[m2025-09-18 14:21:01.178 [36mINFO [m [35m[main][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mAttempting to connect to: [**************:5672]
[m2025-09-18 14:21:01.797 [36mINFO [m [35m[main][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mCreated new connection: rabbitConnectionFactory#2ce9f29c:0/SimpleConnection@30fa5713 [delegate=amqp://admin@**************:5672/, localPort= 58200]
[m2025-09-18 14:21:02.499 [36mINFO [m [35m[main][m [36mc.h.AppMain[m [34m[][m - [32mStarted AppMain in 122.242 seconds (JVM running for 132.642)
[m2025-09-18 14:21:02.501 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 14:21:02.502 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 14:21:07.086 [36mINFO [m [35m[main][m [36mc.h.s.c.s.c.SsoCache[m [34m[][m - [32minit cache_data -> size 21 --> ["user_circle","role","org_job_user","user_resources","project","resources","user_leave","resources_user","user_all","user_role_meet","circle_user","user_role","role_resources","police","role_user","organization","user_org_role","job","circle","user","organization_tree"] -> 2025-09-18 09:44:25
[m2025-09-18 14:21:07.091 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 14:21:07.091 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 14:21:07.399 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosContextRefresher[m [34m[][m - [32m[Nacos Config] Listening config: dataId=hl-wj-police-archive.properties, group=default
[m2025-09-18 14:21:07.400 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosContextRefresher[m [34m[][m - [32m[Nacos Config] Listening config: dataId=hl-wj-police-archive-druid.properties, group=default
[m2025-09-18 14:21:07.400 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosContextRefresher[m [34m[][m - [32m[Nacos Config] Listening config: dataId=hl-wj-police-archive, group=default
[m2025-09-18 14:21:07.472 [36mINFO [m [35m[main][m [36mc.h.d.s.i.DictDataServiceImpl[m [34m[][m - [32mrefresh dict cache 
[m2025-09-18 14:21:08.551 [36mINFO [m [35m[RMI TCP Connection(9)-*************][m [36mo.a.c.c.C.[.[.[/][m [34m[][m - [32mInitializing Spring DispatcherServlet 'dispatcherServlet'
[m2025-09-18 14:21:08.552 [36mINFO [m [35m[RMI TCP Connection(9)-*************][m [36mo.s.w.s.DispatcherServlet[m [34m[][m - [32mInitializing Servlet 'dispatcherServlet'
[m2025-09-18 14:21:08.631 [36mINFO [m [35m[RMI TCP Connection(9)-*************][m [36mo.s.w.s.DispatcherServlet[m [34m[][m - [32mCompleted initialization in 79 ms
[m2025-09-18 14:21:08.860 [36mINFO [m [35m[main][m [36mc.h.l.c.l.LogSql[m [34m[][m - [32mExecute SQL：SELECT id,dict_type,parent_id,dict_value,dict_name,sort,field_class,remark,create_user,create_time,update_user,update_time,is_disable,tenant_id,is_system,is_default,extend FROM dict_data
[m2025-09-18 14:21:09.502 [33mWARN [m [35m[RMI TCP Connection(8)-*************][m [36mo.s.b.a.e.ElasticsearchRestClientHealthIndicator[m [34m[][m - [33mElasticsearch health check failed
[m java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor144.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-09-18 14:21:15.576 [31mERROR[m [35m[main][m [36mc.a.d.f.s.StatFilter[m [34m[][m - [31mslow sql 2121 millis. SELECT 1[]
[m2025-09-18 14:21:19.099 [36mINFO [m [35m[main][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-1} inited
[m2025-09-18 14:21:23.593 [36mINFO [m [35m[RMI TCP Connection(8)-*************][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-2} inited
[m2025-09-18 14:21:27.904 [36mINFO [m [35m[RMI TCP Connection(8)-*************][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-3} inited
[m2025-09-18 14:21:33.331 [36mINFO [m [35m[RMI TCP Connection(8)-*************][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-4} inited
[m2025-09-18 14:24:20.827 [33mWARN [m [35m[AMQP Connection **************:5672][m [36mc.r.c.i.ForgivingExceptionHandler[m [34m[][m - [33mAn unexpected connection driver error occurred (Exception message: Connection reset)
[m2025-09-18 14:24:21.462 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mRestarting Consumer@3886be2b: tags=[[amq.ctag-td1NuO-YWfa_gZKggCYk3w]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@**************:5672/,1), conn: Proxy@49f50482 Shared Rabbit Connection: SimpleConnection@30fa5713 [delegate=amqp://admin@**************:5672/, localPort= 58200], acknowledgeMode=MANUAL local queue size=0
[m2025-09-18 14:24:21.475 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mAttempting to connect to: [**************:5672]
[m2025-09-18 14:24:47.633 [33mWARN [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [33mConsumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
[m2025-09-18 14:24:47.633 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mRestarting Consumer@8330cc0: tags=[[]], channel=null, acknowledgeMode=MANUAL local queue size=0
[m2025-09-18 14:24:47.639 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-3][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mAttempting to connect to: [**************:5672]
[m2025-09-18 14:25:13.762 [33mWARN [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-3][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [33mConsumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
[m2025-09-18 14:25:13.762 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-3][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mRestarting Consumer@48139078: tags=[[]], channel=null, acknowledgeMode=MANUAL local queue size=0
[m2025-09-18 14:25:13.769 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-4][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mAttempting to connect to: [**************:5672]
[m2025-09-18 14:25:39.860 [33mWARN [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-4][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [33mConsumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
[m2025-09-18 14:25:39.860 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-4][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mRestarting Consumer@5f7a3049: tags=[[]], channel=null, acknowledgeMode=MANUAL local queue size=0
[m2025-09-18 14:25:39.873 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-5][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mAttempting to connect to: [**************:5672]
[m2025-09-18 14:26:06.020 [33mWARN [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-5][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [33mConsumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
[m2025-09-18 14:26:06.020 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-5][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mRestarting Consumer@1e797782: tags=[[]], channel=null, acknowledgeMode=MANUAL local queue size=0
[m2025-09-18 14:26:06.025 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-6][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mAttempting to connect to: [**************:5672]
[m2025-09-18 14:26:32.150 [33mWARN [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-6][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [33mConsumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
[m2025-09-18 14:26:32.151 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-6][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mRestarting Consumer@3aea606: tags=[[]], channel=null, acknowledgeMode=MANUAL local queue size=0
[m2025-09-18 14:26:32.173 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-7][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mAttempting to connect to: [**************:5672]
[m2025-09-18 14:26:35.194 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-7][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mCreated new connection: rabbitConnectionFactory#2ce9f29c:6/SimpleConnection@5790e32d [delegate=amqp://admin@**************:5672/, localPort= 57694]
[m2025-09-18 14:33:16.298 [36mINFO [m [35m[http-nio-28183-exec-1][m [36mc.h.l.c.l.LogSql[m [34m[][m - [32mExecute SQL：SELECT category_name, feature_name, COUNT(*) as count FROM police_capability_eval WHERE is_deleted = 0 AND category_name IS NOT NULL AND category_name != '' AND feature_name IS NOT NULL AND feature_name != '' GROUP BY category_name, feature_name ORDER BY category_name, COUNT(*) DESC
[m2025-09-18 14:33:16.984 [36mINFO [m [35m[http-nio-28183-exec-1][m [36mc.h.l.LogSlf4j[m [34m[][m - [32m127.0.0.1:/capabilityEval/statistics (局长指令) -> {} -> 478ms -> {"count":0,"data":[{"categoryName":"专业打击","categoryTotalCount":911,"featureStatistics":[{"featureCount":317,"featureName":"侦查研判"},{"featureCount":310,"featureName":"审讯突破"},{"featureCount":100,"featureName":"获取线索"},{"featureCount":59,"featureName":"监所安全管理"},{"featureCount":51,"featureName":"检验鉴定"},{"featureCount":47,"featureName":"实战指挥"},{"featureCount":21,"featureName":"现场勘查"},{"featureCount":6,"featureName":"监所协助破案"}]},{"categoryName":"基础防控","categoryTotalCount":618,"featureStatistics":[{"featureCount":208,"featureName":"安全监管"},{"featureCount":205,"featureName":"治安要素管理"},{"featureCount":139,"featureName":"矛盾化解"},{"featureCount":45,"featureName":"社会动员"},{"featureCount":16,"featureName":"人员因私出入境管理"},{"featureCount":5,"featureName":"涉稳事件处置"}]},{"categoryName":"执法监督","categoryTotalCount":581,"featureStatistics":[{"featureCount":363,"featureName":"案件审核（办理）"},{"featureCount":99,"featureName":"信访举报核查"},{"featureCount":85,"featureName":"执法监督指导"},{"featureCount":21,"featureName":"现场督察"},{"feat
[m2025-09-18 14:41:07.237 [33mWARN [m [35m[Thread-26][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-18 14:41:07.237 [33mWARN [m [35m[Thread-33][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-18 14:41:07.238 [33mWARN [m [35m[Thread-33][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-18 14:41:07.245 [33mWARN [m [35m[Thread-26][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m2025-09-18 14:41:08.824 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-8][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mWaiting for workers to finish.
[m2025-09-18 14:41:08.826 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mo.s.b.w.e.t.GracefulShutdown[m [34m[][m - [32mCommencing graceful shutdown. Waiting for active requests to complete
[m2025-09-18 14:41:08.952 [36mINFO [m [35m[tomcat-shutdown][m [36mo.s.b.w.e.t.GracefulShutdown[m [34m[][m - [32mGraceful shutdown complete
[m2025-09-18 14:41:09.531 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-8][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mSuccessfully waited for workers to finish.
[m2025-09-18 14:41:09.596 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.c.n.r.NacosServiceRegistry[m [34m[][m - [32mDe-registering from Nacos Server now...
[m2025-09-18 14:41:09.603 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.c.n.r.NacosServiceRegistry[m [34m[][m - [32mDe-registration finished.
[m2025-09-18 14:41:09.921 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.h.c.t.GraceThreadPool[m [34m[][m - [32m==== 优雅关闭线程池 ====
[m2025-09-18 14:41:09.947 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-4} closing ...
[m2025-09-18 14:41:09.957 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-4} closed
[m2025-09-18 14:41:09.958 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-3} closing ...
[m2025-09-18 14:41:09.958 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-3} closed
[m2025-09-18 14:41:09.959 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-2} closing ...
[m2025-09-18 14:41:09.960 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-2} closed
[m2025-09-18 14:41:09.961 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-1} closing ...
[m2025-09-18 14:41:09.981 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-1} closed
[m