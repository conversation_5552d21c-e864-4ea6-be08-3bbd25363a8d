2025-09-18 14:19:09.241 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-18 14:19:09.250 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-18 14:19:09.256 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-18 14:19:13.624 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.624 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.625 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.625 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.625 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.625 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.626 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.626 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.626 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.626 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:24.486 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-18 14:19:24.487 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.505 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-18 14:19:24.506 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.523 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-18 14:19:24.523 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.541 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-18 14:19:24.542 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.557 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-18 14:19:24.557 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.574 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-18 14:19:24.575 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.593 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-18 14:19:24.593 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.609 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-18 14:19:24.609 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.625 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-18 14:19:24.626 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.643 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-18 14:19:24.644 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.662 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-18 14:19:24.663 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.681 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-18 14:19:24.682 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.700 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-18 14:19:24.736 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-18 14:19:24.737 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.760 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-18 14:19:24.760 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.778 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-18 14:19:24.779 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.799 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjLzfx".
[m2025-09-18 14:19:24.800 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjLzfx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.816 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-18 14:19:24.817 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.836 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-18 14:19:24.836 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.856 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-18 14:19:24.857 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.878 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-18 14:19:24.879 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.898 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-18 14:19:24.898 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.915 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-18 14:19:24.916 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.932 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-18 14:19:24.932 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.948 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-18 14:19:24.949 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.001 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-18 14:19:25.001 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.029 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-18 14:19:25.029 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.046 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-18 14:19:25.046 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.062 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-18 14:19:25.062 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.078 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-18 14:19:25.080 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.095 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-18 14:19:25.095 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.114 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-18 14:19:25.114 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.130 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-18 14:19:25.130 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.145 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-18 14:19:25.146 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.164 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-18 14:19:25.164 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.180 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-18 14:19:25.181 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.197 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-18 14:19:25.198 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.216 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-18 14:19:25.216 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.228 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-18 14:19:25.230 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.257 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-18 14:19:25.257 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.280 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-18 14:19:25.281 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.297 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-18 14:19:25.297 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.316 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-18 14:19:25.316 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.335 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-18 14:19:25.336 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.354 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-18 14:19:25.355 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.372 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-18 14:19:25.372 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.389 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-18 14:19:25.389 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.406 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-18 14:19:25.406 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.428 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjYjbb".
[m2025-09-18 14:19:25.428 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjYjbb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.446 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-18 14:19:25.447 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.475 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-18 14:19:25.475 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.507 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-18 14:19:25.508 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.539 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-18 14:19:25.539 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.559 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-18 14:19:25.559 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.578 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-18 14:19:25.578 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:46.845 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:19:46.846 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:19:50.001 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:19:50.002 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:19:53.179 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:19:53.179 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:19:56.365 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:19:56.365 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:19:59.542 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:19:59.542 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:20:02.719 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:20:02.719 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:20:05.905 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:20:05.906 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:20:09.261 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:20:09.262 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:20:13.021 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:20:13.021 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:20:16.428 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:20:16.430 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:21:09.502 [33mWARN [m [35m[RMI TCP Connection(8)-100.88.176.35][m [36mo.s.b.a.e.ElasticsearchRestClientHealthIndicator[m [34m[][m - [33mElasticsearch health check failed
[m java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor144.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-09-18 14:21:15.576 [31mERROR[m [35m[main][m [36mc.a.d.f.s.StatFilter[m [34m[][m - [31mslow sql 2121 millis. SELECT 1[]
[m2025-09-18 14:24:20.827 [33mWARN [m [35m[AMQP Connection 192.168.10.104:5672][m [36mc.r.c.i.ForgivingExceptionHandler[m [34m[][m - [33mAn unexpected connection driver error occurred (Exception message: Connection reset)
[m2025-09-18 14:24:47.633 [33mWARN [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [33mConsumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
[m2025-09-18 14:25:13.762 [33mWARN [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-3][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [33mConsumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
[m2025-09-18 14:25:39.860 [33mWARN [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-4][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [33mConsumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
[m2025-09-18 14:26:06.020 [33mWARN [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-5][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [33mConsumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
[m2025-09-18 14:26:32.150 [33mWARN [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-6][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [33mConsumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
[m2025-09-18 14:41:07.237 [33mWARN [m [35m[Thread-26][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-18 14:41:07.237 [33mWARN [m [35m[Thread-33][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-18 14:41:07.238 [33mWARN [m [35m[Thread-33][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-18 14:41:07.245 [33mWARN [m [35m[Thread-26][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m