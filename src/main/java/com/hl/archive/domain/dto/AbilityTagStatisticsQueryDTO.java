package com.hl.archive.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 能力标签统计查询请求DTO
 */
@Data
@ApiModel(description = "能力标签统计查询请求DTO")
public class AbilityTagStatisticsQueryDTO {

    /**
     * 组织ID
     */
    @ApiModelProperty(value = "组织ID")
    private String organizationId;

    /**
     * 一级分类名称（可选，用于筛选特定一级分类）
     */
    @ApiModelProperty(value = "一级分类名称")
    private String firstLevelCategory;

    /**
     * 二级分类名称（可选，用于筛选特定二级分类）
     */
    @ApiModelProperty(value = "二级分类名称")
    private String secondLevelCategory;

    /**
     * 三级分类名称（可选，用于筛选特定三级分类）
     */
    @ApiModelProperty(value = "三级分类名称")
    private String thirdLevelCategory;

    /**
     * 能力标签代码（可选，用于筛选特定标签代码）
     */
    @ApiModelProperty(value = "能力标签代码")
    private String abilityTagCode;

    /**
     * 能力标签名称（可选，用于筛选特定标签名称）
     */
    @ApiModelProperty(value = "能力标签名称")
    private String abilityTagName;
}
