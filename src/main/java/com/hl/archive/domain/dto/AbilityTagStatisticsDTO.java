package com.hl.archive.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 能力标签统计返回DTO
 */
@Data
@ApiModel(description = "能力标签统计返回DTO")
public class AbilityTagStatisticsDTO {

    /**
     * 一级分类名称
     */
    @ApiModelProperty(value = "一级分类名称")
    private String firstLevelCategory;

    /**
     * 一级分类总人数
     */
    @ApiModelProperty(value = "一级分类总人数")
    private Integer firstLevelTotalCount = 0;

    /**
     * 二级分类统计列表
     */
    @ApiModelProperty(value = "二级分类统计列表")
    private List<SecondLevelStatisticsDTO> secondLevelStatistics;

    /**
     * 二级分类统计详情
     */
    @Data
    @ApiModel(description = "二级分类统计详情")
    public static class SecondLevelStatisticsDTO {
        
        /**
         * 二级分类名称
         */
        @ApiModelProperty(value = "二级分类名称")
        private String secondLevelCategory;

        /**
         * 二级分类人数
         */
        @ApiModelProperty(value = "二级分类人数")
        private Integer secondLevelCount = 0;

        /**
         * 三级分类（能力标签）统计列表
         */
        @ApiModelProperty(value = "三级分类统计列表")
        private List<ThirdLevelStatisticsDTO> thirdLevelStatistics;
    }

    /**
     * 三级分类（能力标签）统计详情
     */
    @Data
    @ApiModel(description = "三级分类统计详情")
    public static class ThirdLevelStatisticsDTO {
        
        /**
         * 三级分类名称（能力标签名称）
         */
        @ApiModelProperty(value = "三级分类名称")
        private String thirdLevelCategory;

        /**
         * 三级分类人数
         */
        @ApiModelProperty(value = "三级分类人数")
        private Integer thirdLevelCount = 0;

        /**
         * 原始字典代码
         */
        @ApiModelProperty(value = "原始字典代码")
        private String dictCode;
    }
}
