package com.hl.archive.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.dto.CapabilityEvalStatisticsDTO;
import com.hl.archive.domain.dto.CapabilityEvalStatisticsQueryDTO;
import com.hl.archive.domain.dto.PoliceCapabilityEvalRequestDTO;
import com.hl.archive.domain.entity.PoliceCapabilityEval;
import com.hl.archive.mapper.PoliceCapabilityEvalMapper;
import com.hl.archive.utils.SsoCacheUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class PoliceCapabilityEvalService extends ServiceImpl<PoliceCapabilityEvalMapper, PoliceCapabilityEval> {

    private final PoliceCapabilityEvalMapper policeCapabilityEvalMapper;

    public Page<PoliceCapabilityEval> pageList(PoliceCapabilityEvalRequestDTO requestDTO) {
        LambdaQueryWrapper<PoliceCapabilityEval> queryWrapper = Wrappers.<PoliceCapabilityEval>lambdaQuery()
                .eq(StrUtil.isNotBlank(requestDTO.getPoliceNumber()), PoliceCapabilityEval::getPoliceNumber, requestDTO.getPoliceNumber());

        if (StrUtil.isNotBlank(requestDTO.getOrganizationId())) {
            if ("32041200000".equals(requestDTO.getOrganizationId())) {
                queryWrapper.eq(PoliceCapabilityEval::getOrganizationId, requestDTO.getOrganizationId());
            } else {
                queryWrapper.like(PoliceCapabilityEval::getOrganizationId, requestDTO.getOrganizationId().substring(0, 8));
            }
        }
        if (StrUtil.isNotBlank(requestDTO.getQuery())) {
            queryWrapper.and(w -> w.like(PoliceCapabilityEval::getParticipantName, requestDTO.getQuery())
                    .or()
                    .like(PoliceCapabilityEval::getPoliceNumber, requestDTO.getQuery())
                    .or()
                    .like(PoliceCapabilityEval::getPlanName, requestDTO.getQuery())
                    .or()
                    .like(PoliceCapabilityEval::getFeatureName, requestDTO.getQuery()));
        }
        Page<PoliceCapabilityEval> page = this.page(Page.of(requestDTO.getPage(), requestDTO.getLimit()), queryWrapper);
        return page;
    }



    public void cleanOrganizationId() {
        this.list().forEach(item -> {
            item.setOrganizationId(SsoCacheUtil.getUserOrgIdByPoliceId(item.getPoliceNumber()));
            this.updateById(item);
        });
    }

    /**
     * 根据 categoryName 和 featureName 进行统计（数据库层统计）
     */
    public List<CapabilityEvalStatisticsDTO> getCapabilityEvalStatistics(CapabilityEvalStatisticsQueryDTO request) {
        // 调用数据库层统计查询
        List<Map<String, Object>> statisticsData = policeCapabilityEvalMapper.getCapabilityEvalStatistics(request);

        // 使用 LinkedHashMap 保持插入顺序
        Map<String, CapabilityEvalStatisticsDTO> categoryMap = new LinkedHashMap<>();

        // 处理查询结果
        for (Map<String, Object> row : statisticsData) {
            String categoryName = (String) row.get("category_name");
            String featureName = (String) row.get("feature_name");
            Integer count = ((Number) row.get("count")).intValue();

            // 获取或创建大类统计对象
            CapabilityEvalStatisticsDTO categoryStatistics = categoryMap.get(categoryName);
            if (categoryStatistics == null) {
                categoryStatistics = new CapabilityEvalStatisticsDTO();
                categoryStatistics.setCategoryName(categoryName);
                categoryStatistics.setCategoryTotalCount(0);
                categoryStatistics.setFeatureStatistics(new ArrayList<>());
                categoryMap.put(categoryName, categoryStatistics);
            }

            // 累加大类总人数
            categoryStatistics.setCategoryTotalCount(categoryStatistics.getCategoryTotalCount() + count);

            // 创建特征统计对象
            CapabilityEvalStatisticsDTO.FeatureStatisticsDTO featureStatistics =
                    new CapabilityEvalStatisticsDTO.FeatureStatisticsDTO();
            featureStatistics.setFeatureName(featureName);
            featureStatistics.setFeatureCount(count);

            categoryStatistics.getFeatureStatistics().add(featureStatistics);
        }

        // 转换为列表并排序
        List<CapabilityEvalStatisticsDTO> resultList = new ArrayList<>(categoryMap.values());

        // 按大类总人数降序排序
        resultList.sort((a, b) -> b.getCategoryTotalCount().compareTo(a.getCategoryTotalCount()));

        // 对每个大类内的特征按人数降序排序（SQL已经排序，这里是保险）
        for (CapabilityEvalStatisticsDTO categoryStatistics : resultList) {
            categoryStatistics.getFeatureStatistics().sort((a, b) ->
                    b.getFeatureCount().compareTo(a.getFeatureCount()));
        }

        return resultList;
    }
}
