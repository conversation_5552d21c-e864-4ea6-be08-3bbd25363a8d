package com.hl.archive.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.dto.CapabilityEvalStatisticsDTO;
import com.hl.archive.domain.dto.CapabilityEvalStatisticsQueryDTO;
import com.hl.archive.domain.dto.PoliceCapabilityEvalRequestDTO;
import com.hl.archive.domain.entity.PoliceCapabilityEval;
import com.hl.archive.mapper.PoliceCapabilityEvalMapper;
import com.hl.archive.utils.SsoCacheUtil;
import com.hl.common.constants.ProjectCommonConstants;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class PoliceCapabilityEvalService extends ServiceImpl<PoliceCapabilityEvalMapper, PoliceCapabilityEval> {

    public Page<PoliceCapabilityEval> pageList(PoliceCapabilityEvalRequestDTO requestDTO) {
        LambdaQueryWrapper<PoliceCapabilityEval> queryWrapper = Wrappers.<PoliceCapabilityEval>lambdaQuery()
                .eq(StrUtil.isNotBlank(requestDTO.getPoliceNumber()), PoliceCapabilityEval::getPoliceNumber, requestDTO.getPoliceNumber());

        if (StrUtil.isNotBlank(requestDTO.getOrganizationId())) {
            if ("32041200000".equals(requestDTO.getOrganizationId())) {
                queryWrapper.eq(PoliceCapabilityEval::getOrganizationId, requestDTO.getOrganizationId());
            } else {
                queryWrapper.like(PoliceCapabilityEval::getOrganizationId, requestDTO.getOrganizationId().substring(0, 8));
            }
        }
        if (StrUtil.isNotBlank(requestDTO.getQuery())) {
            queryWrapper.and(w -> w.like(PoliceCapabilityEval::getParticipantName, requestDTO.getQuery())
                    .or()
                    .like(PoliceCapabilityEval::getPoliceNumber, requestDTO.getQuery())
                    .or()
                    .like(PoliceCapabilityEval::getPlanName, requestDTO.getQuery())
                    .or()
                    .like(PoliceCapabilityEval::getFeatureName, requestDTO.getQuery()));
        }
        Page<PoliceCapabilityEval> page = this.page(Page.of(requestDTO.getPage(), requestDTO.getLimit()), queryWrapper);
        return page;
    }



    public void cleanOrganizationId() {
        this.list().forEach(item -> {
            item.setOrganizationId(SsoCacheUtil.getUserOrgIdByPoliceId(item.getPoliceNumber()));
            this.updateById(item);
        });
    }

    /**
     * 根据 categoryName 和 featureName 进行统计
     */
    public List<CapabilityEvalStatisticsDTO> getCapabilityEvalStatistics(CapabilityEvalStatisticsQueryDTO request) {
        // 构建查询条件
        LambdaQueryWrapper<PoliceCapabilityEval> queryWrapper = Wrappers.<PoliceCapabilityEval>lambdaQuery();

        // 组织ID处理
        if (StrUtil.isNotBlank(request.getOrganizationId())) {
            if (ProjectCommonConstants.WU_JIN_ORG_CODE.equals(request.getOrganizationId())) {
                // 320412000000时查询全部数据
                queryWrapper.eq(PoliceCapabilityEval::getOrganizationId, request.getOrganizationId());
            } else {
                // 其他情况按前8位模糊匹配
                queryWrapper.like(PoliceCapabilityEval::getOrganizationId, request.getOrganizationId().substring(0, 8));
            }
        }

        // 其他筛选条件
        if (StrUtil.isNotBlank(request.getCategoryName())) {
            queryWrapper.eq(PoliceCapabilityEval::getCategoryName, request.getCategoryName());
        }
        if (StrUtil.isNotBlank(request.getFeatureName())) {
            queryWrapper.eq(PoliceCapabilityEval::getFeatureName, request.getFeatureName());
        }
        if (StrUtil.isNotBlank(request.getEvalStatus())) {
            queryWrapper.eq(PoliceCapabilityEval::getEvalStatus, request.getEvalStatus());
        }
        if (StrUtil.isNotBlank(request.getReviewResult())) {
            queryWrapper.eq(PoliceCapabilityEval::getReviewResult, request.getReviewResult());
        }

        // 查询数据
        List<PoliceCapabilityEval> dataList = this.list(queryWrapper);

        // 按 categoryName 分组统计
        Map<String, List<PoliceCapabilityEval>> categoryGroupMap = dataList.stream()
                .filter(item -> StrUtil.isNotBlank(item.getCategoryName()))
                .collect(Collectors.groupingBy(PoliceCapabilityEval::getCategoryName));

        List<CapabilityEvalStatisticsDTO> resultList = new ArrayList<>();

        for (Map.Entry<String, List<PoliceCapabilityEval>> categoryEntry : categoryGroupMap.entrySet()) {
            String categoryName = categoryEntry.getKey();
            List<PoliceCapabilityEval> categoryDataList = categoryEntry.getValue();

            CapabilityEvalStatisticsDTO categoryStatistics = new CapabilityEvalStatisticsDTO();
            categoryStatistics.setCategoryName(categoryName);
            categoryStatistics.setCategoryTotalCount(categoryDataList.size());

            // 按 featureName 分组统计
            Map<String, List<PoliceCapabilityEval>> featureGroupMap = categoryDataList.stream()
                    .filter(item -> StrUtil.isNotBlank(item.getFeatureName()))
                    .collect(Collectors.groupingBy(PoliceCapabilityEval::getFeatureName));

            List<CapabilityEvalStatisticsDTO.FeatureStatisticsDTO> featureStatisticsList = new ArrayList<>();

            for (Map.Entry<String, List<PoliceCapabilityEval>> featureEntry : featureGroupMap.entrySet()) {
                String featureName = featureEntry.getKey();
                List<PoliceCapabilityEval> featureDataList = featureEntry.getValue();

                CapabilityEvalStatisticsDTO.FeatureStatisticsDTO featureStatistics =
                        new CapabilityEvalStatisticsDTO.FeatureStatisticsDTO();
                featureStatistics.setFeatureName(featureName);
                featureStatistics.setFeatureCount(featureDataList.size());

                featureStatisticsList.add(featureStatistics);
            }

            // 按特征人数降序排序
            featureStatisticsList.sort((a, b) -> b.getFeatureCount().compareTo(a.getFeatureCount()));

            categoryStatistics.setFeatureStatistics(featureStatisticsList);
            resultList.add(categoryStatistics);
        }

        // 按大类总人数降序排序
        resultList.sort((a, b) -> b.getCategoryTotalCount().compareTo(a.getCategoryTotalCount()));

        return resultList;
    }
}
