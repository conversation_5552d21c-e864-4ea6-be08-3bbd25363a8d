package com.hl.archive.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.common.log.SnailJobLog;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.dto.AbilityTagStatisticsDTO;
import com.hl.archive.domain.dto.AbilityTagStatisticsQueryDTO;
import com.hl.archive.domain.entity.PoliceAbilityTag;
import com.hl.archive.feign.TaskApi;
import com.hl.archive.listener.config.PoliceAbilityTagConfig;
import com.hl.archive.listener.event.PoliceAbilityTagDeleteEvent;
import com.hl.archive.listener.event.PoliceAbilityTagEvent;
import com.hl.archive.mapper.PoliceAbilityTagMapper;
import com.hl.archive.utils.AbilityDictUtils;
import com.hl.archive.utils.SsoCacheUtil;
import com.hl.common.domain.R;
import com.hl.dict.DictCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 民警职业能力标签服务接口
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PoliceAbilityTagService extends ServiceImpl<PoliceAbilityTagMapper, PoliceAbilityTag> {

    @Value("${spring.security.sso.projectToken}")
    private String token;

    @Resource
    private TaskApi taskApi;

    private final PoliceAbilityTagConfig policeAbilityTagConfig;


    @Resource
    @Qualifier("datasource2DataSource")
    private DataSource taskDataSource;

    @EventListener(PoliceAbilityTagEvent.class)
    public void parseTaskAbilityTag(PoliceAbilityTagEvent event) {
        try {
            JSONObject taskData = event.getTaskData();
            log.info("职业能力标签:{}",taskData);
            String opt = taskData.getByPath("opt").toString();
            if (!"audit".equals(opt)){
                return;
            }
            String passStatus= taskData.getByPath("data.content.pass").toString();
            if (!"1".equals(passStatus)){
                return;
            }
            // 处理内容数据
            handleContentData(taskData);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
    }

    private void handleContentData(JSONObject taskData) {
        String taskId = taskData.getByPath("data.task_id").toString();
        JSONObject param = new JSONObject();
        param.put("task_id", taskId);
        R<?> oneTask = taskApi.getOneTask(token, param);
        JSONObject parsed = JSONObject.from(oneTask.getData());
        JSONObject content = parsed.getJSONObject("all_content");
        List<String> abilityTagCode = content.getList(policeAbilityTagConfig.getAbilityTagCode(), String.class);
        List<String> abilityTagName = new ArrayList<>();
        for (String tagCode : abilityTagCode) {
            if (DictCache.ID_MAP_TREE.containsKey(tagCode)) {
                String dictName = DictCache.ID_MAP_TREE.getJSONObject(tagCode).getString("name");
                abilityTagName.add(dictName);
            }
        }
        List<String> idCard = content.getList(policeAbilityTagConfig.getIdCard(), String.class);
        String description = content.getString(policeAbilityTagConfig.getDescription());
        String obtainTime = content.getString(policeAbilityTagConfig.getObtainTime());
        for (String s : abilityTagCode) {
            for (String i : idCard) {
                PoliceAbilityTag policeAbilityTag = new PoliceAbilityTag();
                if (DictCache.ID_MAP_TREE.containsKey(s)) {
                    String dictName = DictCache.ID_MAP_TREE.getJSONObject(s).getString("name");
                    policeAbilityTag.setAbilityTagName(dictName);
                }
                policeAbilityTag.setAbilityTagCode(s);
                policeAbilityTag.setIdCard(i);
                policeAbilityTag.setDescription(description);
                policeAbilityTag.setObtainTime(DateUtil.parse(obtainTime).toLocalDateTime().toLocalDate());
                policeAbilityTag.setTaskId(taskId);
                log.info("任务:{} 审批通过,新增职业能力标签:{}",taskId,policeAbilityTag);
                this.save(policeAbilityTag);
            }
        }


    }


    @EventListener(PoliceAbilityTagDeleteEvent.class)
    public void handleDeleteEvent(PoliceAbilityTagDeleteEvent event) {
        String taskId = null;
        try {
            JSONObject taskData = event.getTaskData();
            taskId = taskData.getByPath("data.task_id").toString();
            log.info("任务:{} 删除职业能力标签", taskId);
            this.remove(Wrappers.<PoliceAbilityTag>lambdaQuery()
                    .eq(PoliceAbilityTag::getTaskId, taskId));
        } catch (Exception e) {
            log.info("任务:{} 删除职业能力标签失败", taskId);
            log.error(e.getMessage(), e);
        }
    }


    @JobExecutor(name = "handleAbilityTag")
    public void handleAbilityTag(JobArgs jobArgs) {
        try {
            Object jobParams = jobArgs.getJobParams();
            if (jobParams == null) {
                SnailJobLog.REMOTE.info("职业能力标签任务参数为空");
                return;
            }
            JSONObject param = JSONObject.parseObject(jobParams.toString());
            List<String> taskId = param.getList("taskId", String.class);
            if (taskId == null || taskId.isEmpty()) {
                SnailJobLog.REMOTE.info("职业能力标签任务id为空");
                return;
            }
            String taskStr = taskId.stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
            List<Entity> query = DbUtil.use(taskDataSource).query("select * from view_archive_task_event where opt = 'audit' and task_id in (?) ", taskStr);
            for (Entity entity : query) {
                String contentData = entity.getStr("content_data");
                JSONObject contentDataObj = JSONObject.parseObject(contentData);
                SnailJobLog.REMOTE.info("职业能力补偿任务处理:{}", contentDataObj);
                handleContentData(contentDataObj);
            }
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
    }


    @JobExecutor(name = "cleanTagInfo")
    public void cleanTagInfo(){
        List<PoliceAbilityTag> list = this.list();
        for (PoliceAbilityTag policeAbilityTag : list) {
            String abilityTagCode = policeAbilityTag.getAbilityTagCode();
            String abilityTagName = policeAbilityTag.getAbilityTagName();
            String idCard = policeAbilityTag.getIdCard();
            policeAbilityTag.setAbilityTagCode(abilityTagCode.replaceAll("^\\[\"|\"\\]$", ""));
            policeAbilityTag.setAbilityTagName(abilityTagName.replaceAll("^\\[\"|\"\\]$", ""));
            policeAbilityTag.setIdCard(idCard.replaceAll("^\\[\"|\"\\]$", ""));
            this.updateById(policeAbilityTag);
        }
    }


    @JobExecutor(name = "cleanTagOrganizationId")
    public void cleanOrganizationId(){
        List<PoliceAbilityTag> list = this.list();
        for (PoliceAbilityTag policeAbilityTag : list) {
            policeAbilityTag.setOrganizationId(SsoCacheUtil.getUserOrgIdByIdCard(policeAbilityTag.getIdCard()));
            this.updateById(policeAbilityTag);
        }
    }

    /**
     * 根据能力标签代码进行层级统计（数据库层统计）
     */
    public List<AbilityTagStatisticsDTO> getAbilityTagStatistics(AbilityTagStatisticsQueryDTO request) {
        // 调用数据库层统计查询
        List<Map<String, Object>> statisticsData = baseMapper.getAbilityTagStatistics(request);

        // 使用 LinkedHashMap 保持插入顺序
        Map<String, AbilityTagStatisticsDTO> firstLevelMap = new LinkedHashMap<>();

        // 处理查询结果
        for (Map<String, Object> row : statisticsData) {
            String abilityTagCode = (String) row.get("ability_tag_code");
            String abilityTagName = (String) row.get("ability_tag_name");
            Integer count = ((Number) row.get("count")).intValue();

            // 通过字典代码获取层级信息
            String firstLevel = AbilityDictUtils.getFirstLevelCategory(abilityTagCode);
            String secondLevel = AbilityDictUtils.getSecondLevelCategory(abilityTagCode);
            String thirdLevel = AbilityDictUtils.getThirdLevelCategory(abilityTagCode);

            // 如果无法获取层级信息，跳过
            if (firstLevel == null) {
                log.warn("无法获取字典代码 {} 的层级信息", abilityTagCode);
                continue;
            }

            // 应用筛选条件
            if (request.getFirstLevelCategory() != null && !request.getFirstLevelCategory().equals(firstLevel)) {
                continue;
            }
            if (request.getSecondLevelCategory() != null && !request.getSecondLevelCategory().equals(secondLevel)) {
                continue;
            }
            if (request.getThirdLevelCategory() != null && !request.getThirdLevelCategory().equals(thirdLevel)) {
                continue;
            }

            // 获取或创建一级分类统计对象
            AbilityTagStatisticsDTO firstLevelStats = firstLevelMap.get(firstLevel);
            if (firstLevelStats == null) {
                firstLevelStats = new AbilityTagStatisticsDTO();
                firstLevelStats.setFirstLevelCategory(firstLevel);
                firstLevelStats.setFirstLevelTotalCount(0);
                firstLevelStats.setSecondLevelStatistics(new ArrayList<>());
                firstLevelMap.put(firstLevel, firstLevelStats);
            }

            // 累加一级分类总人数
            firstLevelStats.setFirstLevelTotalCount(firstLevelStats.getFirstLevelTotalCount() + count);

            // 查找或创建二级分类统计对象
            AbilityTagStatisticsDTO.SecondLevelStatisticsDTO secondLevelStats = null;
            for (AbilityTagStatisticsDTO.SecondLevelStatisticsDTO stats : firstLevelStats.getSecondLevelStatistics()) {
                if (Objects.equals(stats.getSecondLevelCategory(), secondLevel)) {
                    secondLevelStats = stats;
                    break;
                }
            }

            if (secondLevelStats == null) {
                secondLevelStats = new AbilityTagStatisticsDTO.SecondLevelStatisticsDTO();
                secondLevelStats.setSecondLevelCategory(secondLevel);
                secondLevelStats.setSecondLevelCount(0);
                secondLevelStats.setThirdLevelStatistics(new ArrayList<>());
                firstLevelStats.getSecondLevelStatistics().add(secondLevelStats);
            }

            // 累加二级分类人数
            secondLevelStats.setSecondLevelCount(secondLevelStats.getSecondLevelCount() + count);

            // 创建三级分类统计对象
            AbilityTagStatisticsDTO.ThirdLevelStatisticsDTO thirdLevelStats =
                    new AbilityTagStatisticsDTO.ThirdLevelStatisticsDTO();
            thirdLevelStats.setThirdLevelCategory(thirdLevel != null ? thirdLevel : abilityTagName);
            thirdLevelStats.setThirdLevelCount(count);
            thirdLevelStats.setDictCode(abilityTagCode);

            secondLevelStats.getThirdLevelStatistics().add(thirdLevelStats);
        }

        // 转换为列表并排序
        List<AbilityTagStatisticsDTO> resultList = new ArrayList<>(firstLevelMap.values());

        // 按一级分类总人数降序排序
        resultList.sort((a, b) -> b.getFirstLevelTotalCount().compareTo(a.getFirstLevelTotalCount()));

        // 对每个一级分类内的二级分类按人数降序排序
        for (AbilityTagStatisticsDTO firstLevelStats : resultList) {
            firstLevelStats.getSecondLevelStatistics().sort((a, b) ->
                    b.getSecondLevelCount().compareTo(a.getSecondLevelCount()));

            // 对每个二级分类内的三级分类按人数降序排序
            for (AbilityTagStatisticsDTO.SecondLevelStatisticsDTO secondLevelStats : firstLevelStats.getSecondLevelStatistics()) {
                secondLevelStats.getThirdLevelStatistics().sort((a, b) ->
                        b.getThirdLevelCount().compareTo(a.getThirdLevelCount()));
            }
        }

        return resultList;
    }
}
