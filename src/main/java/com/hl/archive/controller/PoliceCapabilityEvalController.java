package com.hl.archive.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.CapabilityEvalStatisticsDTO;
import com.hl.archive.domain.dto.CapabilityEvalStatisticsQueryDTO;
import com.hl.archive.domain.dto.PoliceCapabilityEvalRequestDTO;
import com.hl.archive.domain.entity.PoliceCapabilityEval;
import com.hl.archive.service.PoliceCapabilityEvalService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/capabilityEval")
@RequiredArgsConstructor
@Slf4j
@Api(tags = "能力参评")
public class PoliceCapabilityEvalController {

    private final PoliceCapabilityEvalService policeCapabilityEvalService;

    @PostMapping("/page")
    @ApiOperation("分页查询")
    public R<List<PoliceCapabilityEval>> page(@RequestBody PoliceCapabilityEvalRequestDTO requestDTO){
        Page<PoliceCapabilityEval> page = policeCapabilityEvalService.pageList(requestDTO);
        return R.ok(page.getRecords(),(int) page.getTotal());
    }

    @PostMapping("/statistics")
    @ApiOperation("根据大类名称和特征名称统计")
    public R<List<CapabilityEvalStatisticsDTO>> statistics(@RequestBody CapabilityEvalStatisticsQueryDTO requestDTO){
        List<CapabilityEvalStatisticsDTO> statistics = policeCapabilityEvalService.getCapabilityEvalStatistics(requestDTO);
        return R.ok(statistics);
    }
}
