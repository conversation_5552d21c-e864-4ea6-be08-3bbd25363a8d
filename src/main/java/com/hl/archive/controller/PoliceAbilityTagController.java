package com.hl.archive.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.constant.ProjectCommonConstants;
import com.hl.archive.domain.dto.PoliceBaseQueryDTO;
import com.hl.archive.domain.entity.PoliceAbilityTag;
import com.hl.archive.service.PoliceAbilityTagService;
import com.hl.common.domain.R;
import com.hl.security.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 民警职业能力标签控制器
 */
@RestController
@RequestMapping("/abilityTag")
@Api(tags = "民警职业能力标签")
@RequiredArgsConstructor
public class PoliceAbilityTagController {

    private final PoliceAbilityTagService policeAbilityTagService;

    @PostMapping("/pageAbilityTag")
    @ApiOperation(value = "查询民警职业能力标签列表")
    public R<List<PoliceAbilityTag>> pageAbilityTag(@RequestBody PoliceBaseQueryDTO request) {
        LambdaQueryWrapper<PoliceAbilityTag> queryWrapper = Wrappers.<PoliceAbilityTag>lambdaQuery();
        if (StringUtils.isNotBlank(request.getOrganizationId())) {
            if (!ProjectCommonConstants.WU_JIN_ORG_CODE.equals(request.getOrganizationId())) {
                queryWrapper.like(PoliceAbilityTag::getOrganizationId, request.getOrganizationId().substring(0, 8));
            } else {
                queryWrapper.eq(PoliceAbilityTag::getOrganizationId, request.getOrganizationId());
            }
        }
        queryWrapper.like(StringUtils.isNotBlank(request.getIdCard()), PoliceAbilityTag::getIdCard, request.getIdCard())
                .and(StringUtils.isNotBlank(request.getQuery()), i -> i.like(PoliceAbilityTag::getAbilityTagName, request.getQuery())
                        .or()
                        .like(PoliceAbilityTag::getDescription, request.getQuery()))
                .orderByDesc(PoliceAbilityTag::getObtainTime);
        Page<PoliceAbilityTag> page = policeAbilityTagService.page(
                Page.of(request.getPage(), request.getLimit()), queryWrapper);
        return R.ok(page.getRecords(), (int) page.getTotal());
    }


    @PostMapping("/addAbilityTag")
    @ApiOperation("添加民警职业能力标签")
    public R<Boolean> addAbilityTag(@RequestBody PoliceAbilityTag request) {
        request.setCreatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policeAbilityTagService.save(request));
    }

    @PostMapping("/updateAbilityTag")
    @ApiOperation("更新民警职业能力标签")
    public R<Boolean> updateAbilityTag(@RequestBody PoliceAbilityTag request) {
        // 设置更新时间
        request.setUpdatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policeAbilityTagService.updateById(request));
    }

    @PostMapping("/deleteAbilityTag")
    @ApiOperation("删除民警职业能力标签")
    public R<Boolean> deleteAbilityTag(@RequestBody PoliceAbilityTag request) {
        request.setUpdatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policeAbilityTagService.removeById(request.getId()));
    }


}
